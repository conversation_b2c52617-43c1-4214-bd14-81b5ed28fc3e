const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const { dbUtils, executeQuery } = require('../config/database');
const { authenticate, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().required().messages({
    'string.empty': 'Le nom d\'utilisateur est requis',
    'any.required': 'Le nom d\'utilisateur est requis'
  }),
  password: Joi.string().required().messages({
    'string.empty': 'Le mot de passe est requis',
    'any.required': 'Le mot de passe est requis'
  })
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    'string.empty': 'Le mot de passe actuel est requis',
    'any.required': 'Le mot de passe actuel est requis'
  }),
  newPassword: Joi.string().min(6).required().messages({
    'string.empty': 'Le nouveau mot de passe est requis',
    'string.min': 'Le nouveau mot de passe doit contenir au moins 6 caractères',
    'any.required': 'Le nouveau mot de passe est requis'
  })
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', auditLog('LOGIN_ATTEMPT'), async (req, res) => {
  try {
    // Validate input
    const { error } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { username, password } = req.body;

    // Check if user exists
    const user = await dbUtils.findAll('users', { username }, 
      'id, username, password_hash, full_name, role, is_active, failed_login_attempts, locked_until');
    
    if (user.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Nom d\'utilisateur ou mot de passe incorrect'
      });
    }

    const userData = user[0];

    // Check if account is locked
    if (userData.locked_until && new Date() < new Date(userData.locked_until)) {
      return res.status(423).json({
        success: false,
        message: 'Compte temporairement verrouillé. Réessayez plus tard.'
      });
    }

    // Check if account is active
    if (!userData.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Compte désactivé. Contactez l\'administrateur.'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, userData.password_hash);
    
    if (!isPasswordValid) {
      // Increment failed login attempts
      const failedAttempts = (userData.failed_login_attempts || 0) + 1;
      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
      const lockoutTime = parseInt(process.env.LOCKOUT_TIME) || 15; // minutes
      
      let updateData = { failed_login_attempts: failedAttempts };
      
      // Lock account if max attempts reached
      if (failedAttempts >= maxAttempts) {
        const lockUntil = new Date();
        lockUntil.setMinutes(lockUntil.getMinutes() + lockoutTime);
        updateData.locked_until = lockUntil;
      }
      
      await dbUtils.updateById('users', userData.id, updateData);
      
      return res.status(401).json({
        success: false,
        message: 'Nom d\'utilisateur ou mot de passe incorrect'
      });
    }

    // Reset failed login attempts and update last login
    await dbUtils.updateById('users', userData.id, {
      failed_login_attempts: 0,
      locked_until: null,
      last_login: new Date()
    });

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: userData.id,
        username: userData.username,
        role: userData.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Log successful login
    logger.info(`Connexion réussie pour l'utilisateur: ${username}`);

    res.json({
      success: true,
      message: 'Connexion réussie',
      data: {
        token,
        user: {
          id: userData.id,
          username: userData.username,
          fullName: userData.full_name,
          role: userData.role
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la connexion'
    });
  }
});

// @route   POST /api/auth/change-password
// @desc    Change user password
// @access  Private
router.post('/change-password', authenticate, auditLog('CHANGE_PASSWORD'), async (req, res) => {
  try {
    // Validate input
    const { error } = changePasswordSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Get current user data
    const user = await dbUtils.findById('users', userId, 'password_hash');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    
    if (!isCurrentPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Mot de passe actuel incorrect'
      });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await dbUtils.updateById('users', userId, {
      password_hash: newPasswordHash,
      updated_at: new Date()
    });

    logger.info(`Mot de passe changé pour l'utilisateur ID: ${userId}`);

    res.json({
      success: true,
      message: 'Mot de passe changé avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors du changement de mot de passe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du changement de mot de passe'
    });
  }
});

// @route   GET /api/auth/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile', authenticate, async (req, res) => {
  try {
    const user = await dbUtils.findById('users', req.user.id, 
      'id, username, full_name, email, role, is_active, last_login, created_at');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        fullName: user.full_name,
        email: user.email,
        role: user.role,
        isActive: user.is_active,
        lastLogin: user.last_login,
        createdAt: user.created_at
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du profil'
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (client-side token removal)
// @access  Private
router.post('/logout', authenticate, auditLog('LOGOUT'), (req, res) => {
  logger.info(`Déconnexion de l'utilisateur: ${req.user.username}`);
  
  res.json({
    success: true,
    message: 'Déconnexion réussie'
  });
});

module.exports = router;
