{"name": "abattoir-salem", "version": "1.0.0", "description": "Logiciel de gestion d'abattoir offline - AbattoirSalem", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"wait-on http://localhost:3000 && electron .\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:linux": "electron-builder --linux", "postinstall": "electron-builder install-app-deps", "test-db": "node test-db.js", "setup": "npm install && cd backend && npm install && cd ../frontend && npm install && cd .."}, "keywords": ["abattoir", "gestion", "offline", "electron", "desktop"], "author": "AbattoirSalem Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.0.1"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.4"}, "build": {"appId": "com.abattoirsalem.app", "productName": "AbattoirSalem", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "frontend/build/**/*", "backend/dist/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}