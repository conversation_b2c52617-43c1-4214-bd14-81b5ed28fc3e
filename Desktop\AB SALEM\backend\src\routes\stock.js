const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery, executeTransaction } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const receptionSchema = Joi.object({
  productTypeId: Joi.number().integer().positive().required(),
  receptionDate: Joi.date().required(),
  initialQuantity: Joi.number().integer().positive().required(),
  initialWeight: Joi.number().positive().required(),
  unitPrice: Joi.number().positive().optional(),
  supplierName: Joi.string().max(100).optional(),
  notes: Joi.string().optional(),
  expiryDate: Joi.date().optional()
});

const adjustmentSchema = Joi.object({
  lotId: Joi.number().integer().positive().required(),
  quantityChange: Joi.number().integer().required(),
  weightChange: Joi.number().required(),
  notes: Joi.string().required()
});

// @route   GET /api/stock/lots
// @desc    Get all reception lots
// @access  Private
router.get('/lots', authenticate, async (req, res) => {
  try {
    const { status = 'active', productType, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (status && status !== 'all') {
      whereConditions.push('rl.status = ?');
      params.push(status);
    }

    if (productType) {
      whereConditions.push('rl.product_type_id = ?');
      params.push(productType);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM reception_lots rl 
      ${whereClause}
    `;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get lots with product information
    const query = `
      SELECT 
        rl.*,
        pt.name_fr as product_name_fr,
        pt.name_ar as product_name_ar,
        pt.unit as product_unit,
        u.full_name as created_by_name
      FROM reception_lots rl
      JOIN product_types pt ON rl.product_type_id = pt.id
      JOIN users u ON rl.created_by = u.id
      ${whereClause}
      ORDER BY rl.reception_date DESC, rl.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const lots = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        lots: lots.map(lot => ({
          id: lot.id,
          lotNumber: lot.lot_number,
          productTypeId: lot.product_type_id,
          productName: lot.product_name_fr,
          productNameAr: lot.product_name_ar,
          productUnit: lot.product_unit,
          receptionDate: lot.reception_date,
          initialQuantity: lot.initial_quantity,
          initialWeight: parseFloat(lot.initial_weight),
          currentQuantity: lot.current_quantity,
          currentWeight: parseFloat(lot.current_weight),
          unitPrice: lot.unit_price ? parseFloat(lot.unit_price) : null,
          supplierName: lot.supplier_name,
          notes: lot.notes,
          status: lot.status,
          expiryDate: lot.expiry_date,
          createdBy: lot.created_by_name,
          createdAt: lot.created_at,
          updatedAt: lot.updated_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des lots:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des lots'
    });
  }
});

// @route   POST /api/stock/reception
// @desc    Create new reception lot
// @access  Private (Admin, Magasinier)
router.post('/reception', authenticate, authorize('admin', 'magasinier'), auditLog('CREATE_RECEPTION'), async (req, res) => {
  try {
    // Validate input
    const { error } = receptionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const {
      productTypeId,
      receptionDate,
      initialQuantity,
      initialWeight,
      unitPrice,
      supplierName,
      notes,
      expiryDate
    } = req.body;

    // Verify product type exists
    const productType = await dbUtils.findById('product_types', productTypeId);
    if (!productType || !productType.is_active) {
      return res.status(400).json({
        success: false,
        message: 'Type de produit invalide ou inactif'
      });
    }

    // Generate lot number
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const lotCountQuery = `SELECT COUNT(*) as count FROM reception_lots WHERE DATE(created_at) = CURDATE()`;
    const [lotCountResult] = await executeQuery(lotCountQuery);
    const dailyCount = lotCountResult.count + 1;
    const lotNumber = `LOT${today}${dailyCount.toString().padStart(3, '0')}`;

    // Create reception lot and stock entry in transaction
    const queries = [
      {
        query: `INSERT INTO reception_lots 
          (lot_number, product_type_id, reception_date, initial_quantity, initial_weight, 
           current_quantity, current_weight, unit_price, supplier_name, notes, status, 
           expiry_date, created_by, created_at, updated_at) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, NOW(), NOW())`,
        params: [
          lotNumber, productTypeId, receptionDate, initialQuantity, initialWeight,
          initialQuantity, initialWeight, unitPrice, supplierName, notes,
          expiryDate, req.user.id
        ]
      }
    ];

    const results = await executeTransaction(queries);
    const lotId = results[0].insertId;

    // Create stock entry
    await dbUtils.insert('stock_entries', {
      lot_id: lotId,
      entry_type: 'reception',
      quantity_change: initialQuantity,
      weight_change: initialWeight,
      unit_price: unitPrice,
      notes: notes,
      created_by: req.user.id,
      created_at: new Date()
    });

    // Create stock movement
    await dbUtils.insert('stock_movements', {
      lot_id: lotId,
      movement_type: 'in',
      quantity_change: initialQuantity,
      weight_change: initialWeight,
      reference_type: 'reception',
      reference_id: lotId,
      notes: `Réception initiale - Lot ${lotNumber}`,
      created_by: req.user.id,
      created_at: new Date()
    });

    logger.info(`Nouvelle réception créée: ${lotNumber} (ID: ${lotId})`);

    res.status(201).json({
      success: true,
      message: 'Réception enregistrée avec succès',
      data: {
        id: lotId,
        lotNumber,
        productTypeId,
        initialQuantity,
        initialWeight,
        unitPrice
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création de la réception:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de la réception'
    });
  }
});

// @route   GET /api/stock/summary
// @desc    Get stock summary
// @access  Private
router.get('/summary', authenticate, async (req, res) => {
  try {
    const query = `
      SELECT 
        pt.id as product_type_id,
        pt.name_fr,
        pt.name_ar,
        pt.unit,
        COUNT(rl.id) as total_lots,
        SUM(CASE WHEN rl.status = 'active' THEN rl.current_quantity ELSE 0 END) as available_quantity,
        SUM(CASE WHEN rl.status = 'active' THEN rl.current_weight ELSE 0 END) as available_weight,
        COUNT(CASE WHEN rl.status = 'active' THEN 1 END) as active_lots,
        MIN(CASE WHEN rl.status = 'active' THEN rl.reception_date END) as oldest_stock_date
      FROM product_types pt
      LEFT JOIN reception_lots rl ON pt.id = rl.product_type_id
      WHERE pt.is_active = true
      GROUP BY pt.id, pt.name_fr, pt.name_ar, pt.unit
      ORDER BY pt.name_fr
    `;

    const summary = await executeQuery(query);

    // Get low stock threshold from settings
    const lowStockSetting = await dbUtils.findAll('system_settings', { setting_key: 'low_stock_threshold' });
    const lowStockThreshold = lowStockSetting.length > 0 ? parseFloat(lowStockSetting[0].setting_value) : 10;

    res.json({
      success: true,
      data: {
        summary: summary.map(item => ({
          productTypeId: item.product_type_id,
          productName: item.name_fr,
          productNameAr: item.name_ar,
          unit: item.unit,
          totalLots: item.total_lots || 0,
          availableQuantity: item.available_quantity || 0,
          availableWeight: parseFloat(item.available_weight) || 0,
          activeLots: item.active_lots || 0,
          oldestStockDate: item.oldest_stock_date,
          isLowStock: parseFloat(item.available_weight) < lowStockThreshold
        })),
        lowStockThreshold
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du résumé de stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du résumé de stock'
    });
  }
});

// @route   POST /api/stock/adjustment
// @desc    Create stock adjustment
// @access  Private (Admin, Magasinier)
router.post('/adjustment', authenticate, authorize('admin', 'magasinier'), auditLog('STOCK_ADJUSTMENT'), async (req, res) => {
  try {
    // Validate input
    const { error } = adjustmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { lotId, quantityChange, weightChange, notes } = req.body;

    // Get current lot data
    const lot = await dbUtils.findById('reception_lots', lotId);
    if (!lot) {
      return res.status(404).json({
        success: false,
        message: 'Lot non trouvé'
      });
    }

    if (lot.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'Impossible d\'ajuster un lot inactif'
      });
    }

    // Calculate new quantities
    const newQuantity = lot.current_quantity + quantityChange;
    const newWeight = parseFloat(lot.current_weight) + weightChange;

    if (newQuantity < 0 || newWeight < 0) {
      return res.status(400).json({
        success: false,
        message: 'L\'ajustement ne peut pas résulter en des quantités négatives'
      });
    }

    // Update lot in transaction
    const queries = [
      {
        query: 'UPDATE reception_lots SET current_quantity = ?, current_weight = ?, updated_at = NOW() WHERE id = ?',
        params: [newQuantity, newWeight, lotId]
      }
    ];

    await executeTransaction(queries);

    // Create stock entry
    await dbUtils.insert('stock_entries', {
      lot_id: lotId,
      entry_type: 'adjustment',
      quantity_change: quantityChange,
      weight_change: weightChange,
      notes: notes,
      created_by: req.user.id,
      created_at: new Date()
    });

    // Create stock movement
    await dbUtils.insert('stock_movements', {
      lot_id: lotId,
      movement_type: quantityChange >= 0 ? 'in' : 'out',
      quantity_change: Math.abs(quantityChange),
      weight_change: Math.abs(weightChange),
      reference_type: 'adjustment',
      notes: notes,
      created_by: req.user.id,
      created_at: new Date()
    });

    // Update lot status if depleted
    if (newQuantity === 0 || newWeight === 0) {
      await dbUtils.updateById('reception_lots', lotId, { status: 'depleted' });
    }

    logger.info(`Ajustement de stock effectué sur le lot ${lot.lot_number} (ID: ${lotId})`);

    res.json({
      success: true,
      message: 'Ajustement de stock effectué avec succès',
      data: {
        lotId,
        oldQuantity: lot.current_quantity,
        newQuantity,
        oldWeight: parseFloat(lot.current_weight),
        newWeight,
        quantityChange,
        weightChange
      }
    });

  } catch (error) {
    logger.error('Erreur lors de l\'ajustement de stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'ajustement de stock'
    });
  }
});

module.exports = router;
