import React from 'react';
import { useTranslation } from 'react-i18next';

const ProductsPage = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('products.title')}
        </h1>
        <p className="text-gray-600">
          Gestion des types de produits
        </p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <p className="text-gray-600">
          Module produits à implémenter...
        </p>
      </div>
    </div>
  );
};

export default ProductsPage;
