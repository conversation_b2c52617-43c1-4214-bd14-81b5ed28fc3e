import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../common/LoadingSpinner';
import toast from 'react-hot-toast';

const StockAdjustmentModal = ({ isOpen, onClose, lot }) => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    quantityChange: '',
    weightChange: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  // Create adjustment mutation
  const adjustmentMutation = useMutation(
    (data) => apiService.stock.createAdjustment(data, token),
    {
      onSuccess: (response) => {
        if (response.success) {
          toast.success('Ajustement effectué avec succès');
          onClose();
          setFormData({ quantityChange: '', weightChange: '', notes: '' });
          setErrors({});
          // Invalidate related queries
          queryClient.invalidateQueries('stock-summary');
          queryClient.invalidateQueries('stock-lots');
        } else {
          toast.error(response.message || 'Erreur lors de l\'ajustement');
        }
      },
      onError: (error) => {
        toast.error(error.message || 'Erreur lors de l\'ajustement');
      }
    }
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.quantityChange || formData.quantityChange === '0') {
      newErrors.quantityChange = 'La variation de quantité est requise';
    }
    if (!formData.weightChange || formData.weightChange === '0') {
      newErrors.weightChange = 'La variation de poids est requise';
    }
    if (!formData.notes.trim()) {
      newErrors.notes = 'Une justification est requise';
    }

    // Check if adjustment would result in negative values
    if (lot) {
      const newQuantity = lot.currentQuantity + parseInt(formData.quantityChange || 0);
      const newWeight = lot.currentWeight + parseFloat(formData.weightChange || 0);
      
      if (newQuantity < 0) {
        newErrors.quantityChange = 'La quantité ne peut pas être négative';
      }
      if (newWeight < 0) {
        newErrors.weightChange = 'Le poids ne peut pas être négatif';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submissionData = {
      lotId: lot.id,
      quantityChange: parseInt(formData.quantityChange),
      weightChange: parseFloat(formData.weightChange),
      notes: formData.notes.trim()
    };

    adjustmentMutation.mutate(submissionData);
  };

  const handleClose = () => {
    if (!adjustmentMutation.isLoading) {
      onClose();
      setFormData({ quantityChange: '', weightChange: '', notes: '' });
      setErrors({});
    }
  };

  if (!isOpen || !lot) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Ajustement de stock
            </h3>
            <button
              onClick={handleClose}
              disabled={adjustmentMutation.isLoading}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Lot info */}
          <div className="bg-gray-50 p-3 rounded-md mb-4">
            <p className="text-sm font-medium text-gray-900">
              Lot {lot.lotNumber}
            </p>
            <p className="text-sm text-gray-600">
              {lot.productName}
            </p>
            <div className="mt-2 text-xs text-gray-500">
              <span>Stock actuel: {lot.currentQuantity} pièces, {lot.currentWeight.toFixed(1)} kg</span>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="quantityChange" className="block text-sm font-medium text-gray-700 mb-1">
                Variation de quantité (pièces) *
              </label>
              <input
                type="number"
                id="quantityChange"
                name="quantityChange"
                value={formData.quantityChange}
                onChange={handleInputChange}
                className={`form-input ${errors.quantityChange ? 'border-red-300' : ''}`}
                placeholder="Ex: +10 ou -5"
                disabled={adjustmentMutation.isLoading}
              />
              {errors.quantityChange && (
                <p className="form-error">{errors.quantityChange}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Nouvelle quantité: {lot.currentQuantity + parseInt(formData.quantityChange || 0)} pièces
              </p>
            </div>

            <div>
              <label htmlFor="weightChange" className="block text-sm font-medium text-gray-700 mb-1">
                Variation de poids (kg) *
              </label>
              <input
                type="number"
                id="weightChange"
                name="weightChange"
                value={formData.weightChange}
                onChange={handleInputChange}
                step="0.1"
                className={`form-input ${errors.weightChange ? 'border-red-300' : ''}`}
                placeholder="Ex: +2.5 ou -1.2"
                disabled={adjustmentMutation.isLoading}
              />
              {errors.weightChange && (
                <p className="form-error">{errors.weightChange}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Nouveau poids: {(lot.currentWeight + parseFloat(formData.weightChange || 0)).toFixed(1)} kg
              </p>
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Justification *
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className={`form-textarea ${errors.notes ? 'border-red-300' : ''}`}
                placeholder="Raison de l'ajustement..."
                disabled={adjustmentMutation.isLoading}
              />
              {errors.notes && (
                <p className="form-error">{errors.notes}</p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="btn-outline"
                disabled={adjustmentMutation.isLoading}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="btn-primary flex items-center space-x-2"
                disabled={adjustmentMutation.isLoading}
              >
                {adjustmentMutation.isLoading && <LoadingSpinner size="sm" />}
                <span>Confirmer l'ajustement</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default StockAdjustmentModal;
