@echo off
echo ========================================
echo    AbattoirSalem - Installation
echo ========================================
echo.

echo [1/6] Installation des dependances principales...
call npm install
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation des dependances principales
    pause
    exit /b 1
)

echo.
echo [2/6] Installation des dependances du backend...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation des dependances du backend
    pause
    exit /b 1
)
cd ..

echo.
echo [3/6] Installation des dependances du frontend...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation des dependances du frontend
    pause
    exit /b 1
)
cd ..

echo.
echo [4/6] Creation du fichier de configuration...
if not exist .env (
    copy .env.example .env
    echo Fichier .env cree. Veuillez le modifier avec vos parametres.
) else (
    echo Fichier .env existe deja.
)

echo.
echo [5/6] Creation des dossiers necessaires...
if not exist backups mkdir backups
if not exist logs mkdir logs
if not exist assets mkdir assets

echo.
echo [6/6] Installation terminee !
echo.
echo ========================================
echo    Prochaines etapes :
echo ========================================
echo 1. Configurer MySQL/MariaDB
echo 2. Executer le script db/init.sql
echo 3. Modifier le fichier .env
echo 4. Lancer 'npm run dev' pour demarrer
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
