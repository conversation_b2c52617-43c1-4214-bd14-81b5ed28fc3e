const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery, executeTransaction } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createPaymentSchema = Joi.object({
  clientId: Joi.number().integer().positive().required(),
  orderId: Joi.number().integer().positive().optional(),
  paymentDate: Joi.date().required(),
  amount: Joi.number().positive().required(),
  paymentMethod: Joi.string().valid('cash', 'check', 'transfer', 'other').default('cash'),
  reference: Joi.string().max(100).optional(),
  notes: Joi.string().optional()
});

// @route   GET /api/payments
// @desc    Get all payments
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20, clientId = '', dateFrom = '', dateTo = '', method = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (clientId) {
      whereConditions.push('p.client_id = ?');
      params.push(clientId);
    }

    if (dateFrom) {
      whereConditions.push('p.payment_date >= ?');
      params.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('p.payment_date <= ?');
      params.push(dateTo);
    }

    if (method) {
      whereConditions.push('p.payment_method = ?');
      params.push(method);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM payments p ${whereClause}`;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get payments with client and order information
    const query = `
      SELECT 
        p.*,
        c.name as client_name,
        c.client_code,
        o.order_number,
        u.full_name as created_by_name
      FROM payments p
      JOIN clients c ON p.client_id = c.id
      LEFT JOIN orders o ON p.order_id = o.id
      JOIN users u ON p.created_by = u.id
      ${whereClause}
      ORDER BY p.payment_date DESC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const payments = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        payments: payments.map(payment => ({
          id: payment.id,
          paymentNumber: payment.payment_number,
          clientId: payment.client_id,
          clientName: payment.client_name,
          clientCode: payment.client_code,
          orderId: payment.order_id,
          orderNumber: payment.order_number,
          paymentDate: payment.payment_date,
          amount: parseFloat(payment.amount),
          paymentMethod: payment.payment_method,
          reference: payment.reference,
          notes: payment.notes,
          createdBy: payment.created_by_name,
          createdAt: payment.created_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des paiements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des paiements'
    });
  }
});

// @route   POST /api/payments
// @desc    Create new payment
// @access  Private (Admin, Vendeur)
router.post('/', authenticate, authorize('admin', 'vendeur'), auditLog('CREATE_PAYMENT'), async (req, res) => {
  try {
    // Validate input
    const { error } = createPaymentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { clientId, orderId, paymentDate, amount, paymentMethod, reference, notes } = req.body;

    // Verify client exists
    const client = await dbUtils.findById('clients', clientId);
    if (!client || !client.is_active) {
      return res.status(400).json({
        success: false,
        message: 'Client invalide ou inactif'
      });
    }

    // Verify order exists if provided
    let order = null;
    if (orderId) {
      order = await dbUtils.findById('orders', orderId);
      if (!order || order.client_id !== clientId) {
        return res.status(400).json({
          success: false,
          message: 'Commande invalide ou ne correspond pas au client'
        });
      }
    }

    // Generate payment number
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const paymentCountQuery = `SELECT COUNT(*) as count FROM payments WHERE DATE(created_at) = CURDATE()`;
    const [paymentCountResult] = await executeQuery(paymentCountQuery);
    const dailyCount = paymentCountResult.count + 1;
    const paymentNumber = `PAY${today}${dailyCount.toString().padStart(4, '0')}`;

    // Create payment and update balances in transaction
    const queries = [
      {
        query: `INSERT INTO payments 
          (payment_number, client_id, order_id, payment_date, amount, payment_method, 
           reference, notes, created_by, created_at) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
        params: [paymentNumber, clientId, orderId, paymentDate, amount, paymentMethod, reference, notes, req.user.id]
      }
    ];

    const results = await executeTransaction(queries);
    const paymentId = results[0].insertId;

    // Update client balance
    const newClientBalance = client.current_balance - amount;
    await dbUtils.updateById('clients', clientId, {
      current_balance: Math.max(0, newClientBalance),
      updated_at: new Date()
    });

    // Update order payment status if order specified
    if (order) {
      const newPaidAmount = parseFloat(order.paid_amount) + amount;
      const newRemainingAmount = parseFloat(order.total_amount) - newPaidAmount;
      
      let paymentStatus = 'unpaid';
      if (newRemainingAmount <= 0) {
        paymentStatus = 'paid';
      } else if (newPaidAmount > 0) {
        paymentStatus = 'partial';
      }

      await dbUtils.updateById('orders', orderId, {
        paid_amount: newPaidAmount,
        remaining_amount: Math.max(0, newRemainingAmount),
        payment_status: paymentStatus,
        updated_at: new Date()
      });

      // Update client unpaid orders count if order is now fully paid
      if (paymentStatus === 'paid' && order.payment_status !== 'paid') {
        await executeQuery(
          'UPDATE clients SET unpaid_orders_count = GREATEST(0, unpaid_orders_count - 1), updated_at = NOW() WHERE id = ?',
          [clientId]
        );
      }
    }

    logger.info(`Nouveau paiement créé: ${paymentNumber} (ID: ${paymentId}) - Montant: ${amount}`);

    res.status(201).json({
      success: true,
      message: 'Paiement enregistré avec succès',
      data: {
        id: paymentId,
        paymentNumber,
        clientId,
        orderId,
        amount,
        paymentMethod
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création du paiement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du paiement'
    });
  }
});

// @route   GET /api/payments/summary
// @desc    Get payments summary
// @access  Private
router.get('/summary', authenticate, async (req, res) => {
  try {
    const { dateFrom = '', dateTo = '' } = req.query;

    let whereConditions = [];
    let params = [];

    if (dateFrom) {
      whereConditions.push('payment_date >= ?');
      params.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('payment_date <= ?');
      params.push(dateTo);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get payment summary
    const summaryQuery = `
      SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        payment_method,
        COUNT(*) as method_count,
        SUM(amount) as method_total
      FROM payments 
      ${whereClause}
      GROUP BY payment_method
      WITH ROLLUP
    `;

    const summary = await executeQuery(summaryQuery, params);

    // Get daily payments for the period
    const dailyQuery = `
      SELECT 
        DATE(payment_date) as payment_date,
        COUNT(*) as daily_count,
        SUM(amount) as daily_total
      FROM payments 
      ${whereClause}
      GROUP BY DATE(payment_date)
      ORDER BY payment_date DESC
      LIMIT 30
    `;

    const dailyPayments = await executeQuery(dailyQuery, params);

    // Get top clients by payment amount
    const topClientsQuery = `
      SELECT 
        c.id,
        c.name,
        c.client_code,
        COUNT(p.id) as payment_count,
        SUM(p.amount) as total_paid
      FROM clients c
      JOIN payments p ON c.id = p.client_id
      ${whereClause ? whereClause.replace('payment_date', 'p.payment_date') : ''}
      GROUP BY c.id, c.name, c.client_code
      ORDER BY total_paid DESC
      LIMIT 10
    `;

    const topClients = await executeQuery(topClientsQuery, params);

    res.json({
      success: true,
      data: {
        summary: {
          totalPayments: summary.find(s => s.payment_method === null)?.total_payments || 0,
          totalAmount: parseFloat(summary.find(s => s.payment_method === null)?.total_amount || 0),
          averageAmount: parseFloat(summary.find(s => s.payment_method === null)?.average_amount || 0),
          byMethod: summary.filter(s => s.payment_method !== null).map(s => ({
            method: s.payment_method,
            count: s.method_count,
            total: parseFloat(s.method_total)
          }))
        },
        dailyPayments: dailyPayments.map(dp => ({
          date: dp.payment_date,
          count: dp.daily_count,
          total: parseFloat(dp.daily_total)
        })),
        topClients: topClients.map(tc => ({
          id: tc.id,
          name: tc.name,
          clientCode: tc.client_code,
          paymentCount: tc.payment_count,
          totalPaid: parseFloat(tc.total_paid)
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du résumé des paiements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du résumé des paiements'
    });
  }
});

// @route   GET /api/payments/:id
// @desc    Get payment by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    const paymentQuery = `
      SELECT 
        p.*,
        c.name as client_name,
        c.client_code,
        o.order_number,
        u.full_name as created_by_name
      FROM payments p
      JOIN clients c ON p.client_id = c.id
      LEFT JOIN orders o ON p.order_id = o.id
      JOIN users u ON p.created_by = u.id
      WHERE p.id = ?
    `;

    const [payment] = await executeQuery(paymentQuery, [id]);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Paiement non trouvé'
      });
    }

    res.json({
      success: true,
      data: {
        id: payment.id,
        paymentNumber: payment.payment_number,
        client: {
          id: payment.client_id,
          name: payment.client_name,
          code: payment.client_code
        },
        order: payment.order_id ? {
          id: payment.order_id,
          orderNumber: payment.order_number
        } : null,
        paymentDate: payment.payment_date,
        amount: parseFloat(payment.amount),
        paymentMethod: payment.payment_method,
        reference: payment.reference,
        notes: payment.notes,
        createdBy: payment.created_by_name,
        createdAt: payment.created_at
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du paiement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du paiement'
    });
  }
});

module.exports = router;
