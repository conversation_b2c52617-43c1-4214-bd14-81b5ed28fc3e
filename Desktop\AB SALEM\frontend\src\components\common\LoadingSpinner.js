import React from 'react';
import { useTranslation } from 'react-i18next';

const LoadingSpinner = ({ 
  size = 'md', 
  text = null, 
  fullScreen = false,
  className = '' 
}) => {
  const { t } = useTranslation();

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  const spinner = (
    <div className={`inline-block ${sizeClasses[size]} border-2 border-current border-t-transparent rounded-full animate-spin`} />
  );

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {spinner}
      {(text || text === null) && (
        <div className={`text-gray-600 ${textSizeClasses[size]}`}>
          {text || t('common.loading')}
        </div>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
};

export default LoadingSpinner;
