const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

let connection = null;

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'abattoir_salem',
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  multipleStatements: true
};

async function connectDB() {
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // Test the connection
    await connection.ping();
    
    logger.info('Connexion à la base de données MySQL établie');
    return connection;
  } catch (error) {
    logger.error('Erreur de connexion à la base de données:', error);
    throw error;
  }
}

async function getConnection() {
  if (!connection) {
    await connectDB();
  }
  
  try {
    await connection.ping();
    return connection;
  } catch (error) {
    logger.warn('Connexion perdue, reconnexion...');
    await connectDB();
    return connection;
  }
}

async function executeQuery(query, params = []) {
  try {
    const conn = await getConnection();
    const [results] = await conn.execute(query, params);
    return results;
  } catch (error) {
    logger.error('Erreur lors de l\'exécution de la requête:', error);
    logger.error('Requête:', query);
    logger.error('Paramètres:', params);
    throw error;
  }
}

async function executeTransaction(queries) {
  const conn = await getConnection();
  
  try {
    await conn.beginTransaction();
    
    const results = [];
    for (const { query, params } of queries) {
      const [result] = await conn.execute(query, params || []);
      results.push(result);
    }
    
    await conn.commit();
    return results;
  } catch (error) {
    await conn.rollback();
    logger.error('Erreur lors de la transaction:', error);
    throw error;
  }
}

// Utility functions for common database operations
const dbUtils = {
  // Insert with auto-generated ID return
  async insert(table, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const query = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    const result = await executeQuery(query, values);
    
    return {
      insertId: result.insertId,
      affectedRows: result.affectedRows
    };
  },

  // Update by ID
  async updateById(table, id, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const query = `UPDATE ${table} SET ${setClause} WHERE id = ?`;
    const result = await executeQuery(query, [...values, id]);
    
    return {
      affectedRows: result.affectedRows,
      changedRows: result.changedRows
    };
  },

  // Delete by ID
  async deleteById(table, id) {
    const query = `DELETE FROM ${table} WHERE id = ?`;
    const result = await executeQuery(query, [id]);
    
    return {
      affectedRows: result.affectedRows
    };
  },

  // Find by ID
  async findById(table, id, fields = '*') {
    const query = `SELECT ${fields} FROM ${table} WHERE id = ?`;
    const results = await executeQuery(query, [id]);
    return results[0] || null;
  },

  // Find all with optional conditions
  async findAll(table, conditions = {}, fields = '*', orderBy = 'id ASC', limit = null) {
    let query = `SELECT ${fields} FROM ${table}`;
    const params = [];
    
    if (Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions)
        .map(key => `${key} = ?`)
        .join(' AND ');
      query += ` WHERE ${whereClause}`;
      params.push(...Object.values(conditions));
    }
    
    if (orderBy) {
      query += ` ORDER BY ${orderBy}`;
    }
    
    if (limit) {
      query += ` LIMIT ${limit}`;
    }
    
    return await executeQuery(query, params);
  },

  // Count records
  async count(table, conditions = {}) {
    let query = `SELECT COUNT(*) as count FROM ${table}`;
    const params = [];
    
    if (Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions)
        .map(key => `${key} = ?`)
        .join(' AND ');
      query += ` WHERE ${whereClause}`;
      params.push(...Object.values(conditions));
    }
    
    const result = await executeQuery(query, params);
    return result[0].count;
  }
};

module.exports = {
  connectDB,
  getConnection,
  executeQuery,
  executeTransaction,
  dbUtils
};
