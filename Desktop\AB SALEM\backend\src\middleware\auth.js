const jwt = require('jsonwebtoken');
const { dbUtils } = require('../config/database');
const logger = require('../utils/logger');

// Middleware to verify JWT token
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Accès refusé. Token manquant.'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await dbUtils.findById('users', decoded.id, 'id, username, full_name, role, is_active');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Token invalide. Utilisateur non trouvé.'
        });
      }

      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Compte désactivé.'
        });
      }

      req.user = user;
      next();
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token expiré.'
        });
      }
      
      return res.status(401).json({
        success: false,
        message: 'Token invalide.'
      });
    }
  } catch (error) {
    logger.error('Erreur dans le middleware d\'authentification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification.'
    });
  }
};

// Middleware to check user roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Accès refusé. Authentification requise.'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé. Permissions insuffisantes.'
      });
    }

    next();
  };
};

// Middleware to log user actions for audit
const auditLog = (action) => {
  return async (req, res, next) => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Log the action after successful response
      if (data.success !== false && req.user) {
        logUserAction(req.user.id, action, req, data);
      }
      
      // Call original res.json
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// Function to log user actions
async function logUserAction(userId, action, req, responseData) {
  try {
    const logData = {
      user_id: userId,
      action: action,
      table_name: extractTableName(req.path),
      record_id: extractRecordId(req, responseData),
      old_values: req.body.oldValues ? JSON.stringify(req.body.oldValues) : null,
      new_values: req.method !== 'GET' ? JSON.stringify(req.body) : null,
      ip_address: req.ip || req.connection.remoteAddress,
      user_agent: req.get('User-Agent'),
      created_at: new Date()
    };

    await dbUtils.insert('audit_logs', logData);
  } catch (error) {
    logger.error('Erreur lors de l\'enregistrement du log d\'audit:', error);
  }
}

// Helper function to extract table name from request path
function extractTableName(path) {
  const pathParts = path.split('/');
  if (pathParts.length >= 3) {
    return pathParts[2]; // /api/users -> users
  }
  return null;
}

// Helper function to extract record ID from request or response
function extractRecordId(req, responseData) {
  // Try to get ID from URL params
  if (req.params.id) {
    return parseInt(req.params.id);
  }
  
  // Try to get ID from response data
  if (responseData && responseData.data && responseData.data.id) {
    return responseData.data.id;
  }
  
  // Try to get insertId from response
  if (responseData && responseData.insertId) {
    return responseData.insertId;
  }
  
  return null;
}

module.exports = {
  authenticate,
  authorize,
  auditLog
};
