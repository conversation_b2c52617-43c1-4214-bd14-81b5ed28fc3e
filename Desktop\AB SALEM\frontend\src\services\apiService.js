// API Service for communicating with the backend
class ApiService {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
  }

  // Generic request method
  async request(method, endpoint, data = null, token = null) {
    const url = `${this.baseURL}${endpoint}`;
    const options = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // Add authorization header if token is provided
    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    // Add body for POST, PUT, PATCH requests
    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      
      // Handle different response types
      let result;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        result = await response.json();
      } else {
        result = { success: false, message: 'Invalid response format' };
      }

      // Handle HTTP errors
      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error('API Request failed:', error);
      
      // Handle network errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('Erreur de connexion au serveur');
      }
      
      throw error;
    }
  }

  // Convenience methods
  async get(endpoint, token = null) {
    return this.request('GET', endpoint, null, token);
  }

  async post(endpoint, data, token = null) {
    return this.request('POST', endpoint, data, token);
  }

  async put(endpoint, data, token = null) {
    return this.request('PUT', endpoint, data, token);
  }

  async patch(endpoint, data, token = null) {
    return this.request('PATCH', endpoint, data, token);
  }

  async delete(endpoint, token = null) {
    return this.request('DELETE', endpoint, null, token);
  }

  // Authentication endpoints
  auth = {
    login: (credentials) => this.post('/auth/login', credentials),
    logout: (token) => this.post('/auth/logout', {}, token),
    changePassword: (data, token) => this.post('/auth/change-password', data, token),
    getProfile: (token) => this.get('/auth/profile', token),
  };

  // User management endpoints
  users = {
    getAll: (params, token) => this.get(`/users?${new URLSearchParams(params)}`, token),
    getById: (id, token) => this.get(`/users/${id}`, token),
    create: (data, token) => this.post('/users', data, token),
    update: (id, data, token) => this.put(`/users/${id}`, data, token),
    delete: (id, token) => this.delete(`/users/${id}`, token),
  };

  // Product management endpoints
  products = {
    getAll: (params, token) => this.get(`/products?${new URLSearchParams(params)}`, token),
    getById: (id, token) => this.get(`/products/${id}`, token),
    create: (data, token) => this.post('/products', data, token),
    update: (id, data, token) => this.put(`/products/${id}`, data, token),
    delete: (id, token) => this.delete(`/products/${id}`, token),
    getStockSummary: (id, token) => this.get(`/products/${id}/stock-summary`, token),
  };

  // Stock management endpoints
  stock = {
    getLots: (params, token) => this.get(`/stock/lots?${new URLSearchParams(params)}`, token),
    createReception: (data, token) => this.post('/stock/reception', data, token),
    getSummary: (token) => this.get('/stock/summary', token),
    createAdjustment: (data, token) => this.post('/stock/adjustment', data, token),
  };

  // Client management endpoints
  clients = {
    getAll: (params, token) => this.get(`/clients?${new URLSearchParams(params)}`, token),
    getById: (id, token) => this.get(`/clients/${id}`, token),
    create: (data, token) => this.post('/clients', data, token),
    update: (id, data, token) => this.put(`/clients/${id}`, data, token),
    delete: (id, token) => this.delete(`/clients/${id}`, token),
    getBalance: (id, token) => this.get(`/clients/${id}/balance`, token),
  };

  // Order management endpoints
  orders = {
    getAll: (params, token) => this.get(`/orders?${new URLSearchParams(params)}`, token),
    getById: (id, token) => this.get(`/orders/${id}`, token),
    create: (data, token) => this.post('/orders', data, token),
    update: (id, data, token) => this.put(`/orders/${id}`, data, token),
    updateStatus: (id, status, token) => this.put(`/orders/${id}/status`, { status }, token),
    delete: (id, token) => this.delete(`/orders/${id}`, token),
  };

  // Payment management endpoints
  payments = {
    getAll: (params, token) => this.get(`/payments?${new URLSearchParams(params)}`, token),
    getById: (id, token) => this.get(`/payments/${id}`, token),
    create: (data, token) => this.post('/payments', data, token),
    getSummary: (params, token) => this.get(`/payments/summary?${new URLSearchParams(params)}`, token),
  };

  // Reports endpoints
  reports = {
    getDashboard: (token) => this.get('/reports/dashboard', token),
    getSales: (params, token) => this.get(`/reports/sales?${new URLSearchParams(params)}`, token),
    getStock: (token) => this.get('/reports/stock', token),
    getClients: (token) => this.get('/reports/clients', token),
  };

  // Settings endpoints
  settings = {
    getAll: (token) => this.get('/settings', token),
    getByKey: (key, token) => this.get(`/settings/${key}`, token),
    update: (key, value, token) => this.put(`/settings/${key}`, { settingValue: value }, token),
    createBackup: (token) => this.post('/settings/backup/create', {}, token),
    getBackupList: (token) => this.get('/settings/backup/list', token),
    getAuditLogs: (params, token) => this.get(`/settings/audit-logs?${new URLSearchParams(params)}`, token),
  };
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;
