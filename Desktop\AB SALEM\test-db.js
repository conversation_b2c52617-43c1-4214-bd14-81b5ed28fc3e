const mysql = require('mysql2/promise');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔍 Test de connexion à la base de données...\n');

  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'abattoir_salem'
  };

  console.log('Configuration utilisée :');
  console.log(`- Host: ${config.host}`);
  console.log(`- Port: ${config.port}`);
  console.log(`- User: ${config.user}`);
  console.log(`- Database: ${config.database}`);
  console.log('');

  try {
    // Test de connexion
    console.log('⏳ Connexion en cours...');
    const connection = await mysql.createConnection(config);
    
    // Test de ping
    await connection.ping();
    console.log('✅ Connexion réussie !');

    // Test des tables
    console.log('\n🔍 Vérification des tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    
    if (tables.length === 0) {
      console.log('⚠️  Aucune table trouvée. Exécutez le script db/init.sql');
    } else {
      console.log(`✅ ${tables.length} tables trouvées :`);
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
    }

    // Test de l'utilisateur admin
    console.log('\n🔍 Vérification de l\'utilisateur admin...');
    try {
      const [users] = await connection.execute('SELECT username, role FROM users WHERE username = ?', ['admin']);
      if (users.length > 0) {
        console.log('✅ Utilisateur admin trouvé');
        console.log(`   - Rôle: ${users[0].role}`);
      } else {
        console.log('⚠️  Utilisateur admin non trouvé');
      }
    } catch (error) {
      console.log('⚠️  Table users non trouvée ou erreur:', error.message);
    }

    await connection.end();
    console.log('\n🎉 Test terminé avec succès !');
    
  } catch (error) {
    console.error('\n❌ Erreur de connexion :');
    console.error(`   ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solutions possibles :');
      console.log('   1. Vérifiez que MySQL/MariaDB est démarré');
      console.log('   2. Vérifiez l\'adresse et le port dans .env');
      console.log('   3. Vérifiez les identifiants de connexion');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Solutions possibles :');
      console.log('   1. Créez la base de données avec : mysql -u root -p < db/init.sql');
      console.log('   2. Vérifiez le nom de la base dans .env');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Solutions possibles :');
      console.log('   1. Vérifiez le nom d\'utilisateur et mot de passe dans .env');
      console.log('   2. Accordez les permissions à l\'utilisateur MySQL');
    }
    
    process.exit(1);
  }
}

// Exécuter le test
testDatabaseConnection();
