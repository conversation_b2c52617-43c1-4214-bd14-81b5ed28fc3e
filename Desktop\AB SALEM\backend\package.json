{"name": "abattoir-salem-backend", "version": "1.0.0", "description": "Backend API pour AbattoirSalem", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step needed for Node.js'", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "joi": "^17.11.0", "winston": "^3.11.0", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["api", "abattoir", "mysql", "express"], "author": "AbattoirSalem Team", "license": "MIT"}