import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  isDarkMode: false,
  sidebarCollapsed: false,
  language: 'fr',
};

// Action types
const THEME_ACTIONS = {
  TOGGLE_DARK_MODE: 'TOGGLE_DARK_MODE',
  SET_DARK_MODE: 'SET_DARK_MODE',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  SET_SIDEBAR_COLLAPSED: 'SET_SIDEBAR_COLLAPSED',
  SET_LANGUAGE: 'SET_LANGUAGE',
  LOAD_PREFERENCES: 'LOAD_PREFERENCES',
};

// Reducer
function themeReducer(state, action) {
  switch (action.type) {
    case THEME_ACTIONS.TOGGLE_DARK_MODE:
      return {
        ...state,
        isDarkMode: !state.isDarkMode,
      };
    
    case THEME_ACTIONS.SET_DARK_MODE:
      return {
        ...state,
        isDarkMode: action.payload,
      };
    
    case THEME_ACTIONS.TOGGLE_SIDEBAR:
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed,
      };
    
    case THEME_ACTIONS.SET_SIDEBAR_COLLAPSED:
      return {
        ...state,
        sidebarCollapsed: action.payload,
      };
    
    case THEME_ACTIONS.SET_LANGUAGE:
      return {
        ...state,
        language: action.payload,
      };
    
    case THEME_ACTIONS.LOAD_PREFERENCES:
      return {
        ...state,
        ...action.payload,
      };
    
    default:
      return state;
  }
}

// Create context
const ThemeContext = createContext();

// Provider component
export function ThemeProvider({ children }) {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  // Load preferences from localStorage on mount
  useEffect(() => {
    const loadPreferences = () => {
      try {
        const savedPreferences = localStorage.getItem('themePreferences');
        if (savedPreferences) {
          const preferences = JSON.parse(savedPreferences);
          dispatch({
            type: THEME_ACTIONS.LOAD_PREFERENCES,
            payload: preferences,
          });
        }
      } catch (error) {
        console.error('Error loading theme preferences:', error);
      }
    };

    loadPreferences();
  }, []);

  // Save preferences to localStorage whenever state changes
  useEffect(() => {
    try {
      localStorage.setItem('themePreferences', JSON.stringify(state));
    } catch (error) {
      console.error('Error saving theme preferences:', error);
    }
  }, [state]);

  // Apply dark mode class to document
  useEffect(() => {
    if (state.isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [state.isDarkMode]);

  // Toggle dark mode
  const toggleDarkMode = () => {
    dispatch({ type: THEME_ACTIONS.TOGGLE_DARK_MODE });
  };

  // Set dark mode
  const setDarkMode = (isDark) => {
    dispatch({
      type: THEME_ACTIONS.SET_DARK_MODE,
      payload: isDark,
    });
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    dispatch({ type: THEME_ACTIONS.TOGGLE_SIDEBAR });
  };

  // Set sidebar collapsed state
  const setSidebarCollapsed = (collapsed) => {
    dispatch({
      type: THEME_ACTIONS.SET_SIDEBAR_COLLAPSED,
      payload: collapsed,
    });
  };

  // Set language
  const setLanguage = (language) => {
    dispatch({
      type: THEME_ACTIONS.SET_LANGUAGE,
      payload: language,
    });
  };

  // Get theme classes for components
  const getThemeClasses = () => {
    return {
      background: state.isDarkMode ? 'bg-gray-900' : 'bg-gray-50',
      surface: state.isDarkMode ? 'bg-gray-800' : 'bg-white',
      text: state.isDarkMode ? 'text-gray-100' : 'text-gray-900',
      textSecondary: state.isDarkMode ? 'text-gray-300' : 'text-gray-600',
      border: state.isDarkMode ? 'border-gray-700' : 'border-gray-200',
      hover: state.isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
    };
  };

  const value = {
    ...state,
    toggleDarkMode,
    setDarkMode,
    toggleSidebar,
    setSidebarCollapsed,
    setLanguage,
    getThemeClasses,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
