import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import toast from 'react-hot-toast';

const ClientDetails = () => {
  const { t } = useTranslation();
  const { token, hasRole } = useAuth();
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [activeTab, setActiveTab] = useState('info');
  const [showBlockModal, setShowBlockModal] = useState(false);

  // Fetch client data
  const { data: clientData, isLoading, error } = useQuery(
    ['client', id],
    () => apiService.clients.getById(id, token),
    {
      enabled: !!id && !!token,
    }
  );

  // Fetch client balance details
  const { data: balanceData } = useQuery(
    ['client-balance', id],
    () => apiService.clients.getBalance(id, token),
    {
      enabled: !!id && !!token,
    }
  );

  const client = clientData?.data;
  const balance = balanceData?.data;

  const tabs = [
    { id: 'info', name: 'Informations', icon: '📋' },
    { id: 'orders', name: 'Commandes', icon: '📦' },
    { id: 'payments', name: 'Paiements', icon: '💰' },
    { id: 'balance', name: 'Solde', icon: '📊' },
  ];

  const getStatusBadge = (client) => {
    if (!client?.isActive) {
      return (
        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">
          {t('clients.inactive')}
        </span>
      );
    }
    
    if (client.isBlocked) {
      return (
        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">
          {t('clients.blocked')}
        </span>
      );
    }

    const creditUsage = client.currentBalance / client.creditLimit;
    if (creditUsage >= 1) {
      return (
        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800">
          Limite atteinte
        </span>
      );
    } else if (creditUsage >= 0.8) {
      return (
        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
          Proche limite
        </span>
      );
    }

    return (
      <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
        {t('clients.active')}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !client) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        Client non trouvé ou erreur lors du chargement
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Client Header */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-white">
                  {client.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {client.name}
                </h1>
                <p className="text-sm text-gray-500">
                  Code: {client.clientCode}
                </p>
                <div className="mt-2">
                  {getStatusBadge(client)}
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Link
                to={`/clients/edit/${client.id}`}
                className="btn-outline flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span>{t('common.edit')}</span>
              </Link>
              {hasRole(['admin', 'vendeur']) && (
                <Link
                  to={`/orders/new?clientId=${client.id}`}
                  className="btn-primary flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Nouvelle commande</span>
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('fr-FR', {
                  style: 'currency',
                  currency: 'DZD'
                }).format(client.currentBalance)}
              </div>
              <div className="text-sm text-gray-500">Solde actuel</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('fr-FR', {
                  style: 'currency',
                  currency: 'DZD'
                }).format(client.creditLimit)}
              </div>
              <div className="text-sm text-gray-500">Limite de crédit</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {client.unpaidOrdersCount || 0}
              </div>
              <div className="text-sm text-gray-500">Commandes impayées</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {client.creditLimit > 0 ? 
                  `${((client.currentBalance / client.creditLimit) * 100).toFixed(0)}%` : 
                  '0%'
                }
              </div>
              <div className="text-sm text-gray-500">Crédit utilisé</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'info' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Informations de contact
                  </h3>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Téléphone</dt>
                      <dd className="text-sm text-gray-900">{client.phone || '-'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Email</dt>
                      <dd className="text-sm text-gray-900">{client.email || '-'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Adresse</dt>
                      <dd className="text-sm text-gray-900">{client.address || '-'}</dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Informations du compte
                  </h3>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Date de création</dt>
                      <dd className="text-sm text-gray-900">
                        {new Date(client.createdAt).toLocaleDateString('fr-FR')}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Dernière modification</dt>
                      <dd className="text-sm text-gray-900">
                        {new Date(client.updatedAt).toLocaleDateString('fr-FR')}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Statut</dt>
                      <dd className="text-sm text-gray-900">
                        {client.isActive ? 'Actif' : 'Inactif'}
                        {client.isBlocked && ' - Bloqué'}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>

              {client.notes && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Notes
                  </h3>
                  <p className="text-sm text-gray-700 bg-gray-50 p-4 rounded-md">
                    {client.notes}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'orders' && (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Historique des commandes</h3>
              <p className="mt-1 text-sm text-gray-500">
                L'historique des commandes sera affiché ici une fois le module commandes implémenté.
              </p>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Historique des paiements</h3>
              <p className="mt-1 text-sm text-gray-500">
                L'historique des paiements sera affiché ici une fois le module paiements implémenté.
              </p>
            </div>
          )}

          {activeTab === 'balance' && (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Détail du solde</h3>
              <p className="mt-1 text-sm text-gray-500">
                Le détail du solde et l'historique des transactions seront affichés ici.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientDetails;
