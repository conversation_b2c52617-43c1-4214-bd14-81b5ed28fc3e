import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const StockMovements = () => {
  const { t } = useTranslation();
  const { token } = useAuth();
  
  const [filters, setFilters] = useState({
    movementType: '',
    referenceType: '',
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 20
  });

  // Note: This would need a new API endpoint for stock movements
  // For now, we'll create a placeholder component
  
  const movements = []; // Placeholder data
  const isLoading = false;
  const error = null;

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  const getMovementIcon = (type) => {
    switch (type) {
      case 'in':
        return (
          <div className="bg-green-100 rounded-full p-2">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        );
      case 'out':
        return (
          <div className="bg-red-100 rounded-full p-2">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="bg-gray-100 rounded-full p-2">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
        );
    }
  };

  const getReferenceTypeLabel = (type) => {
    const types = {
      reception: 'Réception',
      order: 'Commande',
      adjustment: 'Ajustement',
      transfer: 'Transfert'
    };
    return types[type] || type;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        Erreur lors du chargement des mouvements
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type de mouvement
            </label>
            <select
              value={filters.movementType}
              onChange={(e) => handleFilterChange('movementType', e.target.value)}
              className="form-select"
            >
              <option value="">Tous les types</option>
              <option value="in">Entrée</option>
              <option value="out">Sortie</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type de référence
            </label>
            <select
              value={filters.referenceType}
              onChange={(e) => handleFilterChange('referenceType', e.target.value)}
              className="form-select"
            >
              <option value="">Toutes les références</option>
              <option value="reception">Réception</option>
              <option value="order">Commande</option>
              <option value="adjustment">Ajustement</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de début
            </label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="form-input"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de fin
            </label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              className="form-input"
            />
          </div>
        </div>
      </div>

      {/* Movements list */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {t('stock.movements')}
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Historique des mouvements de stock
          </p>
        </div>

        {movements.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun mouvement</h3>
            <p className="mt-1 text-sm text-gray-500">
              Les mouvements de stock apparaîtront ici une fois que vous aurez des réceptions et des commandes.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {movements.map((movement) => (
              <div key={movement.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  {getMovementIcon(movement.movementType)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {movement.movementType === 'in' ? 'Entrée' : 'Sortie'} - {getReferenceTypeLabel(movement.referenceType)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Lot {movement.lotNumber} - {movement.productName}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${
                          movement.movementType === 'in' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {movement.movementType === 'in' ? '+' : '-'}{movement.weightChange} kg
                        </p>
                        <p className="text-xs text-gray-500">
                          {movement.quantityChange} pièces
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <span>{new Date(movement.createdAt).toLocaleString('fr-FR')}</span>
                      <span>Par {movement.createdByName}</span>
                    </div>
                    
                    {movement.notes && (
                      <p className="mt-1 text-xs text-gray-600">
                        {movement.notes}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Info message about implementation */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Module en développement
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                L'historique des mouvements sera automatiquement alimenté par :
              </p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Les réceptions de marchandises</li>
                <li>Les sorties pour commandes</li>
                <li>Les ajustements de stock</li>
                <li>Les transferts entre lots</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockMovements;
