const express = require('express');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { dbUtils } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).required(),
  email: Joi.string().email().optional(),
  password: Joi.string().min(6).required(),
  fullName: Joi.string().min(2).max(100).required(),
  role: Joi.string().valid('admin', 'vendeur', 'preparateur', 'magasinier').required()
});

const updateUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).optional(),
  email: Joi.string().email().optional(),
  fullName: Joi.string().min(2).max(100).optional(),
  role: Joi.string().valid('admin', 'vendeur', 'preparateur', 'magasinier').optional(),
  isActive: Joi.boolean().optional()
});

// @route   GET /api/users
// @desc    Get all users
// @access  Private (Admin only)
router.get('/', authenticate, authorize('admin'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (search) {
      whereConditions.push('(username LIKE ? OR full_name LIKE ? OR email LIKE ?)');
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (role) {
      whereConditions.push('role = ?');
      params.push(role);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get users
    const query = `
      SELECT id, username, email, full_name, role, is_active, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    const users = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role,
          isActive: user.is_active,
          lastLogin: user.last_login,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des utilisateurs'
    });
  }
});

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Private (Admin only)
router.get('/:id', authenticate, authorize('admin'), async (req, res) => {
  try {
    const { id } = req.params;

    const user = await dbUtils.findById('users', id, 
      'id, username, email, full_name, role, is_active, last_login, created_at, updated_at');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        isActive: user.is_active,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'utilisateur'
    });
  }
});

// @route   POST /api/users
// @desc    Create new user
// @access  Private (Admin only)
router.post('/', authenticate, authorize('admin'), auditLog('CREATE_USER'), async (req, res) => {
  try {
    // Validate input
    const { error } = createUserSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { username, email, password, fullName, role } = req.body;

    // Check if username already exists
    const existingUser = await dbUtils.findAll('users', { username });
    if (existingUser.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Ce nom d\'utilisateur existe déjà'
      });
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await dbUtils.findAll('users', { email });
      if (existingEmail.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cette adresse email existe déjà'
        });
      }
    }

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userData = {
      username,
      email: email || null,
      password_hash: passwordHash,
      full_name: fullName,
      role,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await dbUtils.insert('users', userData);

    logger.info(`Nouvel utilisateur créé: ${username} (ID: ${result.insertId})`);

    res.status(201).json({
      success: true,
      message: 'Utilisateur créé avec succès',
      data: {
        id: result.insertId,
        username,
        email,
        fullName,
        role
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de l\'utilisateur'
    });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user
// @access  Private (Admin only)
router.put('/:id', authenticate, authorize('admin'), auditLog('UPDATE_USER'), async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateUserSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // Check if user exists
    const existingUser = await dbUtils.findById('users', id);
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    const { username, email, fullName, role, isActive } = req.body;
    const updateData = {};

    // Check username uniqueness if changed
    if (username && username !== existingUser.username) {
      const usernameExists = await dbUtils.findAll('users', { username });
      if (usernameExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Ce nom d\'utilisateur existe déjà'
        });
      }
      updateData.username = username;
    }

    // Check email uniqueness if changed
    if (email && email !== existingUser.email) {
      const emailExists = await dbUtils.findAll('users', { email });
      if (emailExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cette adresse email existe déjà'
        });
      }
      updateData.email = email;
    }

    if (fullName) updateData.full_name = fullName;
    if (role) updateData.role = role;
    if (typeof isActive === 'boolean') updateData.is_active = isActive;
    
    updateData.updated_at = new Date();

    // Update user
    await dbUtils.updateById('users', id, updateData);

    logger.info(`Utilisateur mis à jour: ${existingUser.username} (ID: ${id})`);

    res.json({
      success: true,
      message: 'Utilisateur mis à jour avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour de l\'utilisateur'
    });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user
// @access  Private (Admin only)
router.delete('/:id', authenticate, authorize('admin'), auditLog('DELETE_USER'), async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const user = await dbUtils.findById('users', id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    // Prevent deleting the last admin
    if (user.role === 'admin') {
      const adminCount = await dbUtils.count('users', { role: 'admin', is_active: true });
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Impossible de supprimer le dernier administrateur'
        });
      }
    }

    // Soft delete by deactivating the user instead of hard delete
    await dbUtils.updateById('users', id, { 
      is_active: false,
      updated_at: new Date()
    });

    logger.info(`Utilisateur désactivé: ${user.username} (ID: ${id})`);

    res.json({
      success: true,
      message: 'Utilisateur désactivé avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la suppression de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression de l\'utilisateur'
    });
  }
});

module.exports = router;
