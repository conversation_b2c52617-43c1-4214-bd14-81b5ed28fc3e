const express = require('express');
const { executeQuery } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// @route   GET /api/reports/dashboard
// @desc    Get dashboard statistics
// @access  Private
router.get('/dashboard', authenticate, async (req, res) => {
  try {
    const today = new Date().toISOString().slice(0, 10);
    const thisMonth = new Date().toISOString().slice(0, 7);

    // Today's statistics
    const todayStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE DATE(order_date) = ?) as today_orders,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE DATE(order_date) = ?) as today_sales,
        (SELECT COUNT(*) FROM payments WHERE DATE(payment_date) = ?) as today_payments,
        (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE DATE(payment_date) = ?) as today_payments_amount,
        (SELECT COUNT(*) FROM reception_lots WHERE DATE(reception_date) = ?) as today_receptions
    `;

    const [todayStats] = await executeQuery(todayStatsQuery, [today, today, today, today, today]);

    // This month's statistics
    const monthStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE DATE_FORMAT(order_date, '%Y-%m') = ?) as month_orders,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE DATE_FORMAT(order_date, '%Y-%m') = ?) as month_sales,
        (SELECT COUNT(*) FROM payments WHERE DATE_FORMAT(payment_date, '%Y-%m') = ?) as month_payments,
        (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE DATE_FORMAT(payment_date, '%Y-%m') = ?) as month_payments_amount
    `;

    const [monthStats] = await executeQuery(monthStatsQuery, [thisMonth, thisMonth, thisMonth, thisMonth]);

    // Stock summary
    const stockSummaryQuery = `
      SELECT 
        COUNT(DISTINCT pt.id) as total_products,
        COUNT(rl.id) as total_lots,
        COUNT(CASE WHEN rl.status = 'active' THEN 1 END) as active_lots,
        COALESCE(SUM(CASE WHEN rl.status = 'active' THEN rl.current_weight END), 0) as total_stock_weight
      FROM product_types pt
      LEFT JOIN reception_lots rl ON pt.id = rl.product_type_id
      WHERE pt.is_active = true
    `;

    const [stockSummary] = await executeQuery(stockSummaryQuery);

    // Client statistics
    const clientStatsQuery = `
      SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_clients,
        COUNT(CASE WHEN is_blocked = true THEN 1 END) as blocked_clients,
        COALESCE(SUM(current_balance), 0) as total_outstanding_balance
      FROM clients
    `;

    const [clientStats] = await executeQuery(clientStatsQuery);

    // Recent orders
    const recentOrdersQuery = `
      SELECT 
        o.id, o.order_number, o.order_date, o.total_amount, o.status,
        c.name as client_name, c.client_code
      FROM orders o
      JOIN clients c ON o.client_id = c.id
      ORDER BY o.created_at DESC
      LIMIT 5
    `;

    const recentOrders = await executeQuery(recentOrdersQuery);

    // Low stock alerts
    const lowStockQuery = `
      SELECT 
        pt.name_fr, pt.name_ar,
        COALESCE(SUM(CASE WHEN rl.status = 'active' THEN rl.current_weight END), 0) as current_stock
      FROM product_types pt
      LEFT JOIN reception_lots rl ON pt.id = rl.product_type_id
      WHERE pt.is_active = true
      GROUP BY pt.id, pt.name_fr, pt.name_ar
      HAVING current_stock < 10
      ORDER BY current_stock ASC
    `;

    const lowStockAlerts = await executeQuery(lowStockQuery);

    res.json({
      success: true,
      data: {
        today: {
          orders: todayStats.today_orders,
          sales: parseFloat(todayStats.today_sales),
          payments: todayStats.today_payments,
          paymentsAmount: parseFloat(todayStats.today_payments_amount),
          receptions: todayStats.today_receptions
        },
        thisMonth: {
          orders: monthStats.month_orders,
          sales: parseFloat(monthStats.month_sales),
          payments: monthStats.month_payments,
          paymentsAmount: parseFloat(monthStats.month_payments_amount)
        },
        stock: {
          totalProducts: stockSummary.total_products,
          totalLots: stockSummary.total_lots,
          activeLots: stockSummary.active_lots,
          totalWeight: parseFloat(stockSummary.total_stock_weight)
        },
        clients: {
          total: clientStats.total_clients,
          active: clientStats.active_clients,
          blocked: clientStats.blocked_clients,
          totalOutstanding: parseFloat(clientStats.total_outstanding_balance)
        },
        recentOrders: recentOrders.map(order => ({
          id: order.id,
          orderNumber: order.order_number,
          orderDate: order.order_date,
          totalAmount: parseFloat(order.total_amount),
          status: order.status,
          clientName: order.client_name,
          clientCode: order.client_code
        })),
        lowStockAlerts: lowStockAlerts.map(alert => ({
          productName: alert.name_fr,
          productNameAr: alert.name_ar,
          currentStock: parseFloat(alert.current_stock)
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du tableau de bord:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du tableau de bord'
    });
  }
});

// @route   GET /api/reports/sales
// @desc    Get sales report
// @access  Private
router.get('/sales', authenticate, async (req, res) => {
  try {
    const { dateFrom, dateTo, groupBy = 'day' } = req.query;

    if (!dateFrom || !dateTo) {
      return res.status(400).json({
        success: false,
        message: 'Les dates de début et de fin sont requises'
      });
    }

    let dateFormat;
    switch (groupBy) {
      case 'month':
        dateFormat = '%Y-%m';
        break;
      case 'week':
        dateFormat = '%Y-%u';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const salesQuery = `
      SELECT 
        DATE_FORMAT(order_date, '${dateFormat}') as period,
        COUNT(*) as order_count,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value,
        COUNT(DISTINCT client_id) as unique_clients
      FROM orders
      WHERE order_date >= ? AND order_date <= ? AND status != 'cancelled'
      GROUP BY DATE_FORMAT(order_date, '${dateFormat}')
      ORDER BY period ASC
    `;

    const salesData = await executeQuery(salesQuery, [dateFrom, dateTo]);

    // Top products
    const topProductsQuery = `
      SELECT 
        pt.name_fr, pt.name_ar,
        SUM(od.quantity) as total_quantity,
        SUM(od.weight) as total_weight,
        SUM(od.total_price) as total_revenue,
        COUNT(DISTINCT o.id) as order_count
      FROM order_details od
      JOIN orders o ON od.order_id = o.id
      JOIN product_types pt ON od.product_type_id = pt.id
      WHERE o.order_date >= ? AND o.order_date <= ? AND o.status != 'cancelled'
      GROUP BY pt.id, pt.name_fr, pt.name_ar
      ORDER BY total_revenue DESC
      LIMIT 10
    `;

    const topProducts = await executeQuery(topProductsQuery, [dateFrom, dateTo]);

    // Top clients
    const topClientsQuery = `
      SELECT 
        c.name, c.client_code,
        COUNT(o.id) as order_count,
        SUM(o.total_amount) as total_spent,
        AVG(o.total_amount) as average_order_value
      FROM clients c
      JOIN orders o ON c.id = o.client_id
      WHERE o.order_date >= ? AND o.order_date <= ? AND o.status != 'cancelled'
      GROUP BY c.id, c.name, c.client_code
      ORDER BY total_spent DESC
      LIMIT 10
    `;

    const topClients = await executeQuery(topClientsQuery, [dateFrom, dateTo]);

    res.json({
      success: true,
      data: {
        salesData: salesData.map(item => ({
          period: item.period,
          orderCount: item.order_count,
          totalSales: parseFloat(item.total_sales),
          averageOrderValue: parseFloat(item.average_order_value),
          uniqueClients: item.unique_clients
        })),
        topProducts: topProducts.map(product => ({
          name: product.name_fr,
          nameAr: product.name_ar,
          totalQuantity: product.total_quantity,
          totalWeight: parseFloat(product.total_weight),
          totalRevenue: parseFloat(product.total_revenue),
          orderCount: product.order_count
        })),
        topClients: topClients.map(client => ({
          name: client.name,
          clientCode: client.client_code,
          orderCount: client.order_count,
          totalSpent: parseFloat(client.total_spent),
          averageOrderValue: parseFloat(client.average_order_value)
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la génération du rapport de ventes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la génération du rapport de ventes'
    });
  }
});

// @route   GET /api/reports/stock
// @desc    Get stock report
// @access  Private
router.get('/stock', authenticate, async (req, res) => {
  try {
    // Current stock levels
    const stockLevelsQuery = `
      SELECT 
        pt.id, pt.name_fr, pt.name_ar, pt.unit,
        COUNT(rl.id) as total_lots,
        COUNT(CASE WHEN rl.status = 'active' THEN 1 END) as active_lots,
        COALESCE(SUM(CASE WHEN rl.status = 'active' THEN rl.current_quantity END), 0) as current_quantity,
        COALESCE(SUM(CASE WHEN rl.status = 'active' THEN rl.current_weight END), 0) as current_weight,
        COALESCE(SUM(rl.initial_weight), 0) as total_received,
        MIN(CASE WHEN rl.status = 'active' THEN rl.reception_date END) as oldest_stock_date
      FROM product_types pt
      LEFT JOIN reception_lots rl ON pt.id = rl.product_type_id
      WHERE pt.is_active = true
      GROUP BY pt.id, pt.name_fr, pt.name_ar, pt.unit
      ORDER BY pt.name_fr
    `;

    const stockLevels = await executeQuery(stockLevelsQuery);

    // Stock movements summary
    const movementsQuery = `
      SELECT 
        sm.movement_type,
        sm.reference_type,
        COUNT(*) as movement_count,
        SUM(sm.weight_change) as total_weight_change,
        DATE(sm.created_at) as movement_date
      FROM stock_movements sm
      WHERE sm.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY sm.movement_type, sm.reference_type, DATE(sm.created_at)
      ORDER BY movement_date DESC
    `;

    const movements = await executeQuery(movementsQuery);

    // Expiring lots
    const expiringLotsQuery = `
      SELECT 
        rl.lot_number, rl.expiry_date, rl.current_weight,
        pt.name_fr, pt.name_ar
      FROM reception_lots rl
      JOIN product_types pt ON rl.product_type_id = pt.id
      WHERE rl.status = 'active' 
        AND rl.expiry_date IS NOT NULL 
        AND rl.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
      ORDER BY rl.expiry_date ASC
    `;

    const expiringLots = await executeQuery(expiringLotsQuery);

    res.json({
      success: true,
      data: {
        stockLevels: stockLevels.map(stock => ({
          productId: stock.id,
          productName: stock.name_fr,
          productNameAr: stock.name_ar,
          unit: stock.unit,
          totalLots: stock.total_lots,
          activeLots: stock.active_lots,
          currentQuantity: stock.current_quantity,
          currentWeight: parseFloat(stock.current_weight),
          totalReceived: parseFloat(stock.total_received),
          oldestStockDate: stock.oldest_stock_date
        })),
        movements: movements.map(movement => ({
          movementType: movement.movement_type,
          referenceType: movement.reference_type,
          movementCount: movement.movement_count,
          totalWeightChange: parseFloat(movement.total_weight_change),
          movementDate: movement.movement_date
        })),
        expiringLots: expiringLots.map(lot => ({
          lotNumber: lot.lot_number,
          expiryDate: lot.expiry_date,
          currentWeight: parseFloat(lot.current_weight),
          productName: lot.name_fr,
          productNameAr: lot.name_ar
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la génération du rapport de stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la génération du rapport de stock'
    });
  }
});

// @route   GET /api/reports/clients
// @desc    Get clients report
// @access  Private
router.get('/clients', authenticate, async (req, res) => {
  try {
    // Client summary
    const clientSummaryQuery = `
      SELECT 
        c.*,
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_spent,
        COALESCE(SUM(CASE WHEN o.payment_status = 'paid' THEN o.total_amount END), 0) as total_paid,
        MAX(o.order_date) as last_order_date
      FROM clients c
      LEFT JOIN orders o ON c.id = o.client_id AND o.status != 'cancelled'
      WHERE c.is_active = true
      GROUP BY c.id
      ORDER BY total_spent DESC
    `;

    const clientSummary = await executeQuery(clientSummaryQuery);

    // Payment behavior analysis
    const paymentBehaviorQuery = `
      SELECT 
        CASE 
          WHEN c.current_balance = 0 THEN 'No Outstanding'
          WHEN c.current_balance <= c.credit_limit * 0.5 THEN 'Good Standing'
          WHEN c.current_balance <= c.credit_limit THEN 'Near Limit'
          ELSE 'Over Limit'
        END as payment_category,
        COUNT(*) as client_count,
        SUM(c.current_balance) as total_balance
      FROM clients c
      WHERE c.is_active = true
      GROUP BY payment_category
    `;

    const paymentBehavior = await executeQuery(paymentBehaviorQuery);

    res.json({
      success: true,
      data: {
        clientSummary: clientSummary.map(client => ({
          id: client.id,
          name: client.name,
          clientCode: client.client_code,
          phone: client.phone,
          creditLimit: parseFloat(client.credit_limit),
          currentBalance: parseFloat(client.current_balance),
          isBlocked: client.is_blocked,
          totalOrders: client.total_orders,
          totalSpent: parseFloat(client.total_spent),
          totalPaid: parseFloat(client.total_paid),
          lastOrderDate: client.last_order_date
        })),
        paymentBehavior: paymentBehavior.map(behavior => ({
          category: behavior.payment_category,
          clientCount: behavior.client_count,
          totalBalance: parseFloat(behavior.total_balance)
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la génération du rapport clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la génération du rapport clients'
    });
  }
});

module.exports = router;
