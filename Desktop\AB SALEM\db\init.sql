-- AbattoirSalem Database Schema
-- Version: 1.0.0
-- Date: 2025-01-03

CREATE DATABASE IF NOT EXISTS abattoir_salem CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE abattoir_salem;

-- Table des utilisateurs
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'vendeur', 'preparateur', 'magasinier') NOT NULL DEFAULT 'vendeur',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des types de produits/bêtes
CREATE TABLE product_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_fr VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100),
    description_fr TEXT,
    description_ar TEXT,
    unit ENUM('kg', 'piece', 'both') DEFAULT 'kg',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des lots de réception
CREATE TABLE reception_lots (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lot_number VARCHAR(50) UNIQUE NOT NULL,
    product_type_id INT NOT NULL,
    reception_date DATE NOT NULL,
    initial_quantity INT NOT NULL,
    initial_weight DECIMAL(10,3) NOT NULL,
    current_quantity INT NOT NULL,
    current_weight DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2),
    supplier_name VARCHAR(100),
    notes TEXT,
    status ENUM('active', 'depleted', 'expired') DEFAULT 'active',
    expiry_date DATE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_type_id) REFERENCES product_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Table des entrées de stock
CREATE TABLE stock_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lot_id INT NOT NULL,
    entry_type ENUM('reception', 'adjustment', 'return') NOT NULL,
    quantity_change INT NOT NULL,
    weight_change DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2),
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lot_id) REFERENCES reception_lots(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Table des mouvements de stock
CREATE TABLE stock_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lot_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
    quantity_change INT NOT NULL,
    weight_change DECIMAL(10,3) NOT NULL,
    reference_type ENUM('reception', 'order', 'adjustment', 'waste') NOT NULL,
    reference_id INT,
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lot_id) REFERENCES reception_lots(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Table des clients
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    current_balance DECIMAL(10,2) DEFAULT 0.00,
    unpaid_orders_count INT DEFAULT 0,
    is_blocked BOOLEAN DEFAULT FALSE,
    block_reason VARCHAR(255),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des commandes
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    order_date DATE NOT NULL,
    delivery_date DATE,
    status ENUM('draft', 'confirmed', 'prepared', 'delivered', 'cancelled') DEFAULT 'draft',
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    remaining_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
    notes TEXT,
    created_by INT NOT NULL,
    prepared_by INT,
    delivered_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (prepared_by) REFERENCES users(id),
    FOREIGN KEY (delivered_by) REFERENCES users(id)
);

-- Table des détails de commande
CREATE TABLE order_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_type_id INT NOT NULL,
    quantity INT NOT NULL,
    weight DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_type_id) REFERENCES product_types(id)
);

-- Table des allocations de stock (FIFO)
CREATE TABLE stock_allocations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_detail_id INT NOT NULL,
    lot_id INT NOT NULL,
    allocated_quantity INT NOT NULL,
    allocated_weight DECIMAL(10,3) NOT NULL,
    allocation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_detail_id) REFERENCES order_details(id) ON DELETE CASCADE,
    FOREIGN KEY (lot_id) REFERENCES reception_lots(id)
);

-- Table des paiements
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    order_id INT,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'check', 'transfer', 'other') DEFAULT 'cash',
    reference VARCHAR(100),
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Table des logs d'audit
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Table des paramètres système
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description_fr TEXT,
    description_ar TEXT,
    data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_editable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX idx_reception_lots_date ON reception_lots(reception_date);
CREATE INDEX idx_reception_lots_status ON reception_lots(status);
CREATE INDEX idx_stock_movements_date ON stock_movements(created_at);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_client ON orders(client_id);
CREATE INDEX idx_payments_date ON payments(payment_date);
CREATE INDEX idx_payments_client ON payments(client_id);
CREATE INDEX idx_audit_logs_date ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);

-- Données initiales
INSERT INTO users (username, password_hash, full_name, role) VALUES 
('admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjPoyNdO2', 'Administrateur', 'admin');
-- Mot de passe: admin123

INSERT INTO product_types (name_fr, name_ar, unit) VALUES 
('Bœuf', 'لحم البقر', 'both'),
('Mouton', 'لحم الخروف', 'both'),
('Chèvre', 'لحم الماعز', 'both'),
('Veau', 'لحم العجل', 'both');

INSERT INTO system_settings (setting_key, setting_value, description_fr, data_type) VALUES 
('company_name', 'AbattoirSalem', 'Nom de l\'entreprise', 'string'),
('default_language', 'fr', 'Langue par défaut', 'string'),
('currency', 'DZD', 'Devise', 'string'),
('backup_retention_days', '30', 'Nombre de jours de rétention des sauvegardes', 'number'),
('low_stock_threshold', '10', 'Seuil d\'alerte stock bas (kg)', 'number'),
('max_credit_days', '30', 'Nombre maximum de jours de crédit', 'number'),
('auto_backup_enabled', 'true', 'Sauvegarde automatique activée', 'boolean'),
('backup_time', '02:00', 'Heure de sauvegarde automatique', 'string');
