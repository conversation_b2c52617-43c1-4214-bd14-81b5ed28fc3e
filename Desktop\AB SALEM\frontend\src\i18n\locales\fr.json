{"common": {"loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "print": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "ok": "OK", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Attention", "info": "Information", "required": "Requis", "optional": "Optionnel", "total": "Total", "subtotal": "Sous-total", "quantity": "Quantité", "weight": "Poids", "price": "Prix", "date": "Date", "time": "<PERSON><PERSON>", "status": "Statut", "actions": "Actions", "details": "Détails", "name": "Nom", "description": "Description", "notes": "Notes", "phone": "Téléphone", "email": "Email", "address": "<PERSON><PERSON><PERSON>"}, "auth": {"login": "Connexion", "logout": "Déconnexion", "username": "Nom d'utilisateur", "password": "Mot de passe", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "changePassword": "Changer le mot de passe", "forgotPassword": "Mot de passe oublié ?", "rememberMe": "Se souvenir de moi", "loginButton": "Se connecter", "loginError": "Nom d'utilisateur ou mot de passe incorrect", "loginSuccess": "Connexion réussie", "logoutSuccess": "Déconnexion réussie", "sessionExpired": "Session expirée, ve<PERSON><PERSON>z vous reconnecter", "accessDenied": "<PERSON><PERSON>ès refusé", "passwordChanged": "Mot de passe changé avec succès"}, "navigation": {"dashboard": "Tableau de bord", "stock": "Stock", "reception": "Ré<PERSON>", "inventory": "Inventaire", "clients": "Clients", "orders": "Commandes", "payments": "Paiements", "receptions": "Réceptions", "reports": "Rapports", "settings": "Paramètres", "users": "Utilisateurs", "products": "Produits", "backup": "<PERSON><PERSON><PERSON><PERSON>", "logs": "<PERSON><PERSON><PERSON>"}, "dashboard": {"title": "Tableau de bord", "todayStats": "Statistiques du jour", "monthStats": "Statistiques du mois", "todayOrders": "Commandes aujourd'hui", "todaySales": "Ventes aujourd'hui", "todayPayments": "Paiements aujourd'hui", "monthOrders": "Commandes ce mois", "monthSales": "Ventes ce mois", "stockSummary": "Résumé du stock", "totalProducts": "Produits totaux", "activeLots": "Lots actifs", "totalWeight": "Poids total", "recentOrders": "Commandes récentes", "lowStockAlerts": "Alertes stock bas", "clientsSummary": "Résumé clients"}, "stock": {"title": "Gestion du stock", "overview": "Vue d'ensemble", "reception": "Ré<PERSON>", "lots": "Lots", "movements": "Mouvements", "adjustment": "Ajustement", "lotNumber": "<PERSON>um<PERSON><PERSON>", "productType": "Type de produit", "receptionDate": "Date de réception", "expiryDate": "Date d'expiration", "initialQuantity": "Quantité initiale", "currentQuantity": "Quantité actuelle", "initialWeight": "Poids initial", "currentWeight": "Poids actuel", "unitPrice": "Prix unitaire", "supplier": "Fournisseur", "status": "Statut", "active": "Actif", "depleted": "<PERSON><PERSON><PERSON><PERSON>", "expired": "Expiré", "available": "Disponible", "newReception": "Nouvelle réception", "stockAdjustment": "Ajustement de stock", "lowStock": "Stock bas", "outOfStock": "Rupture de stock", "totalProducts": "Produits totaux", "activeLots": "Lots actifs", "totalWeight": "Poids total", "lowStockAlerts": "Alertes stock bas", "stockByProduct": "Stock par produit", "availableWeight": "Poids disponible", "currentStock": "Stock actuel", "oldestStock": "Stock le plus ancien"}, "clients": {"title": "Gestion des clients", "clientCode": "Code client", "clientName": "Nom du client", "creditLimit": "<PERSON>ite <PERSON>", "currentBalance": "Solde actuel", "unpaidOrders": "Commandes impayées", "isBlocked": "<PERSON><PERSON><PERSON><PERSON>", "blockReason": "<PERSON>son du blocage", "newClient": "Nouveau client", "editClient": "Modifier le client", "clientDetails": "Détails du client", "clientsList": "Liste des clients", "paymentHistory": "Historique des paiements", "orderHistory": "Historique des commandes", "blocked": "<PERSON><PERSON><PERSON><PERSON>", "active": "Actif", "inactive": "Inactif"}, "orders": {"title": "Gestion des commandes", "orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "orderDate": "Date de commande", "deliveryDate": "Date de livraison", "client": "Client", "totalAmount": "Montant total", "paidAmount": "<PERSON><PERSON> payé", "remainingAmount": "<PERSON><PERSON> restant", "paymentStatus": "Statut de paiement", "orderStatus": "Statut de commande", "newOrder": "Nouvelle commande", "editOrder": "Modifier la commande", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "addItem": "Ajouter un article", "removeItem": "Supprimer l'article", "ordersList": "Liste des commandes", "draft": "Brouillon", "confirmed": "Confirmée", "prepared": "Préparée", "delivered": "Livrée", "cancelled": "<PERSON><PERSON><PERSON>", "paid": "Payée", "unpaid": "Impayée", "partial": "Partielle"}, "receptions": {"title": "Gestion des réceptions", "receptionNumber": "Numéro de ré<PERSON>", "receptionDate": "Date de réception", "supplier": "Fournisseur", "deliveryNote": "<PERSON> livraison", "totalQuantity": "Quantité totale", "totalWeight": "Poids total", "status": "Statut", "newReception": "Nouvelle réception", "editReception": "Modifier la réception", "receptionDetails": "<PERSON>é<PERSON> de la réception", "receptionsList": "Liste des réceptions", "pending": "En attente", "received": "Reçue", "processed": "Traitée", "cancelled": "<PERSON><PERSON><PERSON>"}, "payments": {"title": "Gestion des paiements", "paymentNumber": "Numéro de paiement", "paymentDate": "Date de paiement", "amount": "<PERSON><PERSON>", "paymentMethod": "Mode de paiement", "reference": "Référence", "newPayment": "Nouveau paiement", "editPayment": "Modifier le paiement", "paymentsList": "Liste des paiements", "client": "Client", "cashRegister": "Journal de caisse", "cash": "Espèces", "check": "Chèque", "transfer": "Virement", "other": "<PERSON><PERSON>", "paymentHistory": "Historique des paiements", "totalPayments": "Total des paiements", "averagePayment": "<PERSON><PERSON><PERSON> moyen"}, "products": {"title": "Types de produits", "productName": "Nom du produit", "productNameAr": "Nom en arabe", "unit": "Unité", "kg": "Kilogramme", "piece": "<PERSON><PERSON><PERSON>", "both": "Les deux", "newProduct": "Nouveau produit", "editProduct": "Modifier le produit", "beef": "<PERSON><PERSON><PERSON>", "mutton": "Mouton", "goat": "<PERSON><PERSON><PERSON>", "veal": "<PERSON><PERSON>"}, "users": {"title": "Gestion des utilisateurs", "fullName": "Nom complet", "role": "R<PERSON><PERSON>", "lastLogin": "Dernière connexion", "isActive": "Actif", "newUser": "Nouvel utilisateur", "editUser": "Modifier l'utilisateur", "admin": "Administrateur", "vendeur": "<PERSON><PERSON><PERSON>", "preparateur": "Préparateur", "magasinier": "<PERSON><PERSON><PERSON><PERSON>"}, "reports": {"title": "Rapports et analyses", "documents": "Documents", "exports": "Exports", "reportsList": "Rapports", "salesSummary": "Résumé des ventes", "paymentsSummary": "Résumé des paiements", "stockSummary": "État du stock", "clientsSummary": "Analyse clients", "receptionsSummary": "Réceptions", "financialSummary": "Résumé financier", "salesReport": "Rapport des ventes", "stockReport": "Rapport de stock", "clientsReport": "Rapport clients", "paymentsReport": "Rapport des paiements", "dateRange": "Période", "from": "<PERSON>", "to": "Au", "generate": "<PERSON><PERSON><PERSON><PERSON>", "topProducts": "Produits les plus vendus", "topClients": "Meilleurs clients", "dailySales": "Ventes quotidiennes", "monthlySales": "<PERSON><PERSON><PERSON> men<PERSON>"}, "settings": {"title": "Paramètres", "general": "Général", "company": "Entreprise", "system": "Système", "backup": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "Nom de l'entreprise", "currency": "<PERSON><PERSON>", "language": "<PERSON><PERSON>", "timezone": "<PERSON><PERSON> ho<PERSON>", "lowStockThreshold": "Seuil de stock bas", "maxCreditDays": "Jours de crédit maximum", "autoBackup": "Sauvegarde automatique", "backupTime": "<PERSON><PERSON> de sauvegarde", "createBackup": "<PERSON><PERSON><PERSON> une sauvegarde", "restoreBackup": "Restaurer une sauvegarde", "auditLogs": "Journaux d'audit"}, "messages": {"confirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "deleteSuccess": "Élément supprimé avec succès", "saveSuccess": "Enregistré avec succès", "updateSuccess": "Mis à jour avec succès", "createSuccess": "<PERSON><PERSON><PERSON> avec succès", "errorOccurred": "Une erreur s'est produite", "noDataFound": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "invalidInput": "<PERSON><PERSON> invalide", "requiredField": "Ce champ est requis", "networkError": "<PERSON><PERSON><PERSON> <PERSON>", "serverError": "<PERSON><PERSON><PERSON> du <PERSON>", "accessDenied": "<PERSON><PERSON>ès refusé", "sessionExpired": "Session expirée"}}