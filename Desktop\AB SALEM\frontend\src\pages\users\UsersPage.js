import React from 'react';
import { useTranslation } from 'react-i18next';

const UsersPage = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('users.title')}
        </h1>
        <p className="text-gray-600">
          Gestion des utilisateurs et des rôles
        </p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <p className="text-gray-600">
          Module utilisateurs à implémenter...
        </p>
      </div>
    </div>
  );
};

export default UsersPage;
