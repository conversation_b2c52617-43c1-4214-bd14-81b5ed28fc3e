# AbattoirSalem - Logiciel de gestion d'abattoir offline

## 📋 Description

AbattoirSalem est un logiciel desktop professionnel permettant à un abattoir de gérer stock, commandes, clients, paiements et traçabilité, sans connexion internet ni réseau local.

## 🎯 Fonctionnalités principales

- **Gestion du stock** : Réception, lots FIFO, ajustements, alertes
- **Gestion des clients** : Fiches clients, crédit, blocage automatique
- **Commandes & Livraison** : Bons de commande, préparation, livraison, facturation
- **Paiements** : Paiements partiels/complets, journal de caisse
- **Traçabilité** : Suivi complet des produits par lots
- **Rapports** : Tableaux de bord, analyses, exports
- **Multilingue** : Interface français/arabe avec support RTL
- **Sécurité** : Authentification, rôles, logs d'audit
- **Sauvegarde** : Sauvegardes automatiques quotidiennes

## 🏗️ Architecture technique

### Stack technologique
- **Frontend** : React.js + TailwindCSS (via Electron)
- **Backend** : Node.js + Express
- **Base de données** : MySQL/MariaDB local
- **Desktop** : Electron.js
- **Langues** : react-i18next (français/arabe)

### Structure du projet
```
AbattoirSalem/
├── frontend/          # Interface React
├── backend/           # API Node.js
├── db/               # Scripts de base de données
├── backups/          # Sauvegardes automatiques
├── logs/             # Journaux système
├── assets/           # Ressources (icônes, etc.)
├── main.js           # Point d'entrée Electron
├── preload.js        # Script de préchargement
└── .env              # Configuration
```

## 🚀 Installation et démarrage

### Prérequis
- Node.js (v16 ou supérieur)
- MySQL/MariaDB
- npm ou yarn

### Installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd AbattoirSalem
```

2. **Installer les dépendances principales**
```bash
npm install
```

3. **Installer les dépendances du backend**
```bash
cd backend
npm install
cd ..
```

4. **Installer les dépendances du frontend**
```bash
cd frontend
npm install
cd ..
```

5. **Configuration de la base de données**
```bash
# Créer la base de données MySQL
mysql -u root -p < db/init.sql
```

6. **Configuration de l'environnement**
```bash
# Copier et modifier le fichier .env
cp .env.example .env
# Modifier les paramètres de base de données dans .env
```

### Démarrage en développement

1. **Démarrer le backend**
```bash
cd backend
npm run dev
```

2. **Démarrer le frontend** (dans un autre terminal)
```bash
cd frontend
npm start
```

3. **Démarrer Electron** (dans un troisième terminal)
```bash
npm run dev
```

### Build pour production

```bash
# Build complet
npm run build

# Build pour Windows
npm run dist:win

# Build pour Linux
npm run dist:linux
```

## 👥 Rôles utilisateurs

- **Admin** : Accès complet, gestion des utilisateurs, paramètres
- **Vendeur** : Commandes, clients, paiements
- **Préparateur** : Préparation des commandes
- **Magasinier** : Gestion du stock, réceptions

## 🔐 Identifiants par défaut

- **Utilisateur** : admin
- **Mot de passe** : admin123

## 📊 Modules fonctionnels

### 1. Authentification
- Login sécurisé avec JWT
- Gestion des rôles et permissions
- Verrouillage automatique après échecs

### 2. Tableau de bord
- Statistiques en temps réel
- Alertes stock bas
- Commandes récentes

### 3. Gestion du stock
- Réception de marchandises
- Lots avec traçabilité FIFO
- Ajustements et mouvements
- Alertes de seuil

### 4. Gestion des clients
- Fiches clients complètes
- Gestion du crédit et blocage
- Historique des commandes

### 5. Commandes
- Création et suivi des commandes
- Validation automatique du stock
- Génération des documents

### 6. Paiements
- Paiements partiels/complets
- Mise à jour automatique des soldes
- Journal de caisse

### 7. Rapports
- Rapports de ventes
- Analyses de stock
- Rapports clients
- Exports PDF/Excel

### 8. Administration
- Gestion des utilisateurs
- Paramètres système
- Sauvegardes
- Logs d'audit

## 🌐 Support multilingue

L'application supporte le français et l'arabe avec :
- Interface complètement traduite
- Support RTL pour l'arabe
- Polices adaptées
- Formats de date/nombre localisés

## 💾 Sauvegarde

- Sauvegardes automatiques quotidiennes
- Format SQL standard
- Rétention configurable
- Restauration via interface

## 🔒 Sécurité

- Mots de passe hashés (bcrypt)
- Tokens JWT pour les sessions
- Logs d'audit complets
- Protection contre les attaques courantes

## 📝 Logs et audit

Tous les actions importantes sont enregistrées :
- Connexions/déconnexions
- Modifications de données
- Créations/suppressions
- Erreurs système

## 🛠️ Développement

### Structure du code

**Backend** (`/backend/src/`)
- `routes/` : Routes API
- `middleware/` : Middlewares Express
- `config/` : Configuration base de données
- `utils/` : Utilitaires

**Frontend** (`/frontend/src/`)
- `components/` : Composants React
- `pages/` : Pages de l'application
- `contexts/` : Contextes React
- `services/` : Services API
- `i18n/` : Traductions

### Conventions de code

- ESLint pour la qualité du code
- Prettier pour le formatage
- Commits conventionnels
- Tests unitaires recommandés

## 📞 Support

Pour toute question ou problème :
1. Vérifier les logs dans `/logs/`
2. Consulter la documentation
3. Contacter l'équipe de développement

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**AbattoirSalem v1.0.0** - Développé avec ❤️ pour la gestion d'abattoirs
