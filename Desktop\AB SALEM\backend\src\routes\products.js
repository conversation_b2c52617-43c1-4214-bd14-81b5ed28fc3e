const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createProductSchema = Joi.object({
  nameFr: Joi.string().min(2).max(100).required(),
  nameAr: Joi.string().max(100).optional(),
  descriptionFr: Joi.string().optional(),
  descriptionAr: Joi.string().optional(),
  unit: Joi.string().valid('kg', 'piece', 'both').default('kg')
});

const updateProductSchema = Joi.object({
  nameFr: Joi.string().min(2).max(100).optional(),
  nameAr: Joi.string().max(100).optional(),
  descriptionFr: Joi.string().optional(),
  descriptionAr: Joi.string().optional(),
  unit: Joi.string().valid('kg', 'piece', 'both').optional(),
  isActive: Joi.boolean().optional()
});

// @route   GET /api/products
// @desc    Get all product types
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const { active = 'true' } = req.query;
    
    let conditions = {};
    if (active === 'true') {
      conditions.is_active = true;
    }

    const products = await dbUtils.findAll('product_types', conditions, 
      'id, name_fr, name_ar, description_fr, description_ar, unit, is_active, created_at, updated_at',
      'name_fr ASC');

    res.json({
      success: true,
      data: products.map(product => ({
        id: product.id,
        nameFr: product.name_fr,
        nameAr: product.name_ar,
        descriptionFr: product.description_fr,
        descriptionAr: product.description_ar,
        unit: product.unit,
        isActive: product.is_active,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }))
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des produits:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des produits'
    });
  }
});

// @route   GET /api/products/:id
// @desc    Get product type by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    const product = await dbUtils.findById('product_types', id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }

    res.json({
      success: true,
      data: {
        id: product.id,
        nameFr: product.name_fr,
        nameAr: product.name_ar,
        descriptionFr: product.description_fr,
        descriptionAr: product.description_ar,
        unit: product.unit,
        isActive: product.is_active,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du produit'
    });
  }
});

// @route   POST /api/products
// @desc    Create new product type
// @access  Private (Admin, Magasinier)
router.post('/', authenticate, authorize('admin', 'magasinier'), auditLog('CREATE_PRODUCT'), async (req, res) => {
  try {
    // Validate input
    const { error } = createProductSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { nameFr, nameAr, descriptionFr, descriptionAr, unit } = req.body;

    // Check if product name already exists
    const existingProduct = await dbUtils.findAll('product_types', { name_fr: nameFr });
    if (existingProduct.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Un produit avec ce nom existe déjà'
      });
    }

    // Create product
    const productData = {
      name_fr: nameFr,
      name_ar: nameAr || null,
      description_fr: descriptionFr || null,
      description_ar: descriptionAr || null,
      unit: unit || 'kg',
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await dbUtils.insert('product_types', productData);

    logger.info(`Nouveau produit créé: ${nameFr} (ID: ${result.insertId})`);

    res.status(201).json({
      success: true,
      message: 'Produit créé avec succès',
      data: {
        id: result.insertId,
        nameFr,
        nameAr,
        descriptionFr,
        descriptionAr,
        unit
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du produit'
    });
  }
});

// @route   PUT /api/products/:id
// @desc    Update product type
// @access  Private (Admin, Magasinier)
router.put('/:id', authenticate, authorize('admin', 'magasinier'), auditLog('UPDATE_PRODUCT'), async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateProductSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // Check if product exists
    const existingProduct = await dbUtils.findById('product_types', id);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }

    const { nameFr, nameAr, descriptionFr, descriptionAr, unit, isActive } = req.body;
    const updateData = {};

    // Check name uniqueness if changed
    if (nameFr && nameFr !== existingProduct.name_fr) {
      const nameExists = await dbUtils.findAll('product_types', { name_fr: nameFr });
      if (nameExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Un produit avec ce nom existe déjà'
        });
      }
      updateData.name_fr = nameFr;
    }

    if (nameAr !== undefined) updateData.name_ar = nameAr;
    if (descriptionFr !== undefined) updateData.description_fr = descriptionFr;
    if (descriptionAr !== undefined) updateData.description_ar = descriptionAr;
    if (unit) updateData.unit = unit;
    if (typeof isActive === 'boolean') updateData.is_active = isActive;
    
    updateData.updated_at = new Date();

    // Update product
    await dbUtils.updateById('product_types', id, updateData);

    logger.info(`Produit mis à jour: ${existingProduct.name_fr} (ID: ${id})`);

    res.json({
      success: true,
      message: 'Produit mis à jour avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du produit'
    });
  }
});

// @route   DELETE /api/products/:id
// @desc    Delete product type
// @access  Private (Admin only)
router.delete('/:id', authenticate, authorize('admin'), auditLog('DELETE_PRODUCT'), async (req, res) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const product = await dbUtils.findById('product_types', id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }

    // Check if product is used in reception lots
    const lotsCount = await dbUtils.count('reception_lots', { product_type_id: id });
    if (lotsCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Impossible de supprimer ce produit car il est utilisé dans des lots de réception'
      });
    }

    // Soft delete by deactivating the product
    await dbUtils.updateById('product_types', id, { 
      is_active: false,
      updated_at: new Date()
    });

    logger.info(`Produit désactivé: ${product.name_fr} (ID: ${id})`);

    res.json({
      success: true,
      message: 'Produit désactivé avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la suppression du produit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression du produit'
    });
  }
});

// @route   GET /api/products/:id/stock-summary
// @desc    Get stock summary for a product
// @access  Private
router.get('/:id/stock-summary', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const product = await dbUtils.findById('product_types', id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Produit non trouvé'
      });
    }

    // Get stock summary
    const query = `
      SELECT 
        COUNT(*) as total_lots,
        SUM(current_quantity) as total_quantity,
        SUM(current_weight) as total_weight,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_lots,
        COUNT(CASE WHEN status = 'depleted' THEN 1 END) as depleted_lots,
        MIN(reception_date) as oldest_reception,
        MAX(reception_date) as newest_reception
      FROM reception_lots 
      WHERE product_type_id = ?
    `;

    const [summary] = await executeQuery(query, [id]);

    res.json({
      success: true,
      data: {
        productId: parseInt(id),
        productName: product.name_fr,
        totalLots: summary.total_lots || 0,
        totalQuantity: summary.total_quantity || 0,
        totalWeight: parseFloat(summary.total_weight) || 0,
        activeLots: summary.active_lots || 0,
        depletedLots: summary.depleted_lots || 0,
        oldestReception: summary.oldest_reception,
        newestReception: summary.newest_reception
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du résumé de stock:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du résumé de stock'
    });
  }
});

module.exports = router;
