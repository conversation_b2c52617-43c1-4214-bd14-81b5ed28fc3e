import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';

// Contexts
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Components
import ProtectedRoute from './components/common/ProtectedRoute';
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';

// Pages
import LoginPage from './pages/auth/LoginPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import StockPage from './pages/stock/StockPage';
import ClientsPage from './pages/clients/ClientsPage';
import OrdersPage from './pages/orders/OrdersPage';
import PaymentsPage from './pages/payments/PaymentsPage';
import ProductsPage from './pages/products/ProductsPage';
import UsersPage from './pages/users/UsersPage';
import ReportsPage from './pages/reports/ReportsPage';
import SettingsPage from './pages/settings/SettingsPage';
import NotFoundPage from './pages/error/NotFoundPage';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  const { i18n } = useTranslation();

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <div className={`App ${i18n.language === 'ar' ? 'rtl' : 'ltr'}`}>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<LoginPage />} />
                
                {/* Protected routes */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<DashboardPage />} />
                  
                  {/* Stock Management */}
                  <Route path="stock/*" element={<StockPage />} />
                  
                  {/* Client Management */}
                  <Route path="clients/*" element={<ClientsPage />} />
                  
                  {/* Order Management */}
                  <Route path="orders/*" element={<OrdersPage />} />
                  
                  {/* Payment Management */}
                  <Route path="payments/*" element={<PaymentsPage />} />
                  
                  {/* Product Management */}
                  <Route path="products/*" element={<ProductsPage />} />
                  
                  {/* User Management - Admin only */}
                  <Route path="users/*" element={
                    <ProtectedRoute requiredRoles={['admin']}>
                      <UsersPage />
                    </ProtectedRoute>
                  } />
                  
                  {/* Reports */}
                  <Route path="reports/*" element={<ReportsPage />} />
                  
                  {/* Settings - Admin only */}
                  <Route path="settings/*" element={
                    <ProtectedRoute requiredRoles={['admin']}>
                      <SettingsPage />
                    </ProtectedRoute>
                  } />
                </Route>
                
                {/* 404 page */}
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
              
              {/* Toast notifications */}
              <Toaster
                position={i18n.language === 'ar' ? 'top-left' : 'top-right'}
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                  success: {
                    duration: 3000,
                    iconTheme: {
                      primary: '#4ade80',
                      secondary: '#fff',
                    },
                  },
                  error: {
                    duration: 5000,
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#fff',
                    },
                  },
                }}
              />
            </div>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
