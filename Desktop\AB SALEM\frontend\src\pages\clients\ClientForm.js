import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import toast from 'react-hot-toast';

const ClientForm = () => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const navigate = useNavigate();
  const { id } = useParams();
  const queryClient = useQueryClient();
  const isEditing = !!id;

  const [formData, setFormData] = useState({
    name: '',
    clientCode: '',
    phone: '',
    email: '',
    address: '',
    creditLimit: '50000',
    notes: '',
    isActive: true
  });

  const [errors, setErrors] = useState({});

  // Fetch client data for editing
  const { data: clientData, isLoading: clientLoading } = useQuery(
    ['client', id],
    () => apiService.clients.getById(id, token),
    {
      enabled: !!id && !!token,
      onSuccess: (response) => {
        if (response.success) {
          const client = response.data;
          setFormData({
            name: client.name || '',
            clientCode: client.clientCode || '',
            phone: client.phone || '',
            email: client.email || '',
            address: client.address || '',
            creditLimit: client.creditLimit?.toString() || '50000',
            notes: client.notes || '',
            isActive: client.isActive !== false
          });
        }
      }
    }
  );

  // Create/Update client mutation
  const clientMutation = useMutation(
    (data) => {
      if (isEditing) {
        return apiService.clients.update(id, data, token);
      } else {
        return apiService.clients.create(data, token);
      }
    },
    {
      onSuccess: (response) => {
        if (response.success) {
          toast.success(isEditing ? 'Client modifié avec succès' : 'Client créé avec succès');
          queryClient.invalidateQueries('clients');
          navigate('/clients');
        } else {
          toast.error(response.message || 'Erreur lors de l\'enregistrement');
        }
      },
      onError: (error) => {
        toast.error(error.message || 'Erreur lors de l\'enregistrement');
      }
    }
  );

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const generateClientCode = () => {
    const prefix = 'CL';
    const timestamp = Date.now().toString().slice(-6);
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    return `${prefix}${timestamp}${randomNum}`;
  };

  const handleGenerateCode = () => {
    setFormData(prev => ({
      ...prev,
      clientCode: generateClientCode()
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du client est requis';
    }

    if (!formData.clientCode.trim()) {
      newErrors.clientCode = 'Le code client est requis';
    } else if (!/^[A-Z0-9]+$/.test(formData.clientCode)) {
      newErrors.clientCode = 'Le code client doit contenir uniquement des lettres majuscules et des chiffres';
    }

    if (formData.phone && !/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'Format de téléphone invalide';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.creditLimit || parseFloat(formData.creditLimit) < 0) {
      newErrors.creditLimit = 'La limite de crédit doit être positive';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submissionData = {
      name: formData.name.trim(),
      clientCode: formData.clientCode.trim().toUpperCase(),
      phone: formData.phone.trim() || null,
      email: formData.email.trim() || null,
      address: formData.address.trim() || null,
      creditLimit: parseFloat(formData.creditLimit),
      notes: formData.notes.trim() || null,
      isActive: formData.isActive
    };

    clientMutation.mutate(submissionData);
  };

  if (clientLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {isEditing ? t('clients.editClient') : t('clients.newClient')}
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            {isEditing ? 'Modifier les informations du client' : 'Ajouter un nouveau client'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Client Name */}
          <div>
            <label htmlFor="name" className="form-label">
              {t('clients.clientName')} *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`form-input ${errors.name ? 'border-red-300' : ''}`}
              disabled={clientMutation.isLoading}
              placeholder="Nom complet du client"
            />
            {errors.name && (
              <p className="form-error">{errors.name}</p>
            )}
          </div>

          {/* Client Code */}
          <div>
            <label htmlFor="clientCode" className="form-label">
              {t('clients.clientCode')} *
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                id="clientCode"
                name="clientCode"
                value={formData.clientCode}
                onChange={handleInputChange}
                className={`form-input ${errors.clientCode ? 'border-red-300' : ''}`}
                disabled={clientMutation.isLoading}
                placeholder="Ex: CL001"
              />
              <button
                type="button"
                onClick={handleGenerateCode}
                disabled={clientMutation.isLoading}
                className="btn-outline whitespace-nowrap"
              >
                Générer
              </button>
            </div>
            {errors.clientCode && (
              <p className="form-error">{errors.clientCode}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Code unique pour identifier le client
            </p>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="phone" className="form-label">
                {t('common.phone')}
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`form-input ${errors.phone ? 'border-red-300' : ''}`}
                disabled={clientMutation.isLoading}
                placeholder="0555 123 456"
              />
              {errors.phone && (
                <p className="form-error">{errors.phone}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="form-label">
                {t('common.email')}
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`form-input ${errors.email ? 'border-red-300' : ''}`}
                disabled={clientMutation.isLoading}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="form-error">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Address */}
          <div>
            <label htmlFor="address" className="form-label">
              {t('common.address')}
            </label>
            <textarea
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              rows={2}
              className="form-textarea"
              disabled={clientMutation.isLoading}
              placeholder="Adresse complète du client"
            />
          </div>

          {/* Credit Limit */}
          <div>
            <label htmlFor="creditLimit" className="form-label">
              {t('clients.creditLimit')} (DZD) *
            </label>
            <input
              type="number"
              id="creditLimit"
              name="creditLimit"
              value={formData.creditLimit}
              onChange={handleInputChange}
              min="0"
              step="1000"
              className={`form-input ${errors.creditLimit ? 'border-red-300' : ''}`}
              disabled={clientMutation.isLoading}
            />
            {errors.creditLimit && (
              <p className="form-error">{errors.creditLimit}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Montant maximum que le client peut devoir
            </p>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="form-label">
              {t('common.notes')}
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="form-textarea"
              disabled={clientMutation.isLoading}
              placeholder="Notes ou commentaires sur le client"
            />
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              disabled={clientMutation.isLoading}
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              Client actif
            </label>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => navigate('/clients')}
              className="btn-outline"
              disabled={clientMutation.isLoading}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="btn-primary flex items-center space-x-2"
              disabled={clientMutation.isLoading}
            >
              {clientMutation.isLoading && <LoadingSpinner size="sm" />}
              <span>{t('common.save')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ClientForm;
