const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery, executeTransaction } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createOrderSchema = Joi.object({
  clientId: Joi.number().integer().positive().required(),
  orderDate: Joi.date().required(),
  deliveryDate: Joi.date().optional(),
  items: Joi.array().items(
    Joi.object({
      productTypeId: Joi.number().integer().positive().required(),
      quantity: Joi.number().integer().positive().required(),
      weight: Joi.number().positive().required(),
      unitPrice: Joi.number().positive().required(),
      notes: Joi.string().optional()
    })
  ).min(1).required(),
  notes: Joi.string().optional()
});

// @route   GET /api/orders
// @desc    Get all orders
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20, status = '', clientId = '', dateFrom = '', dateTo = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (status) {
      whereConditions.push('o.status = ?');
      params.push(status);
    }

    if (clientId) {
      whereConditions.push('o.client_id = ?');
      params.push(clientId);
    }

    if (dateFrom) {
      whereConditions.push('o.order_date >= ?');
      params.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('o.order_date <= ?');
      params.push(dateTo);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM orders o ${whereClause}`;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get orders with client information
    const query = `
      SELECT 
        o.*,
        c.name as client_name,
        c.client_code,
        u1.full_name as created_by_name,
        u2.full_name as prepared_by_name,
        u3.full_name as delivered_by_name
      FROM orders o
      JOIN clients c ON o.client_id = c.id
      JOIN users u1 ON o.created_by = u1.id
      LEFT JOIN users u2 ON o.prepared_by = u2.id
      LEFT JOIN users u3 ON o.delivered_by = u3.id
      ${whereClause}
      ORDER BY o.order_date DESC, o.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const orders = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        orders: orders.map(order => ({
          id: order.id,
          orderNumber: order.order_number,
          clientId: order.client_id,
          clientName: order.client_name,
          clientCode: order.client_code,
          orderDate: order.order_date,
          deliveryDate: order.delivery_date,
          status: order.status,
          totalAmount: parseFloat(order.total_amount),
          paidAmount: parseFloat(order.paid_amount),
          remainingAmount: parseFloat(order.remaining_amount),
          paymentStatus: order.payment_status,
          notes: order.notes,
          createdBy: order.created_by_name,
          preparedBy: order.prepared_by_name,
          deliveredBy: order.delivered_by_name,
          createdAt: order.created_at,
          updatedAt: order.updated_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des commandes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des commandes'
    });
  }
});

// @route   GET /api/orders/:id
// @desc    Get order by ID with details
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Get order with client information
    const orderQuery = `
      SELECT 
        o.*,
        c.name as client_name,
        c.client_code,
        c.phone as client_phone,
        c.address as client_address,
        u1.full_name as created_by_name,
        u2.full_name as prepared_by_name,
        u3.full_name as delivered_by_name
      FROM orders o
      JOIN clients c ON o.client_id = c.id
      JOIN users u1 ON o.created_by = u1.id
      LEFT JOIN users u2 ON o.prepared_by = u2.id
      LEFT JOIN users u3 ON o.delivered_by = u3.id
      WHERE o.id = ?
    `;

    const [order] = await executeQuery(orderQuery, [id]);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Commande non trouvée'
      });
    }

    // Get order details
    const detailsQuery = `
      SELECT 
        od.*,
        pt.name_fr as product_name,
        pt.name_ar as product_name_ar,
        pt.unit as product_unit
      FROM order_details od
      JOIN product_types pt ON od.product_type_id = pt.id
      WHERE od.order_id = ?
      ORDER BY od.id
    `;

    const details = await executeQuery(detailsQuery, [id]);

    // Get stock allocations for this order
    const allocationsQuery = `
      SELECT 
        sa.*,
        rl.lot_number,
        rl.reception_date
      FROM stock_allocations sa
      JOIN order_details od ON sa.order_detail_id = od.id
      JOIN reception_lots rl ON sa.lot_id = rl.id
      WHERE od.order_id = ?
      ORDER BY sa.order_detail_id, rl.reception_date
    `;

    const allocations = await executeQuery(allocationsQuery, [id]);

    res.json({
      success: true,
      data: {
        id: order.id,
        orderNumber: order.order_number,
        client: {
          id: order.client_id,
          name: order.client_name,
          code: order.client_code,
          phone: order.client_phone,
          address: order.client_address
        },
        orderDate: order.order_date,
        deliveryDate: order.delivery_date,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        paidAmount: parseFloat(order.paid_amount),
        remainingAmount: parseFloat(order.remaining_amount),
        paymentStatus: order.payment_status,
        notes: order.notes,
        createdBy: order.created_by_name,
        preparedBy: order.prepared_by_name,
        deliveredBy: order.delivered_by_name,
        createdAt: order.created_at,
        updatedAt: order.updated_at,
        details: details.map(detail => ({
          id: detail.id,
          productTypeId: detail.product_type_id,
          productName: detail.product_name,
          productNameAr: detail.product_name_ar,
          productUnit: detail.product_unit,
          quantity: detail.quantity,
          weight: parseFloat(detail.weight),
          unitPrice: parseFloat(detail.unit_price),
          totalPrice: parseFloat(detail.total_price),
          notes: detail.notes
        })),
        allocations: allocations.map(allocation => ({
          id: allocation.id,
          orderDetailId: allocation.order_detail_id,
          lotId: allocation.lot_id,
          lotNumber: allocation.lot_number,
          receptionDate: allocation.reception_date,
          allocatedQuantity: allocation.allocated_quantity,
          allocatedWeight: parseFloat(allocation.allocated_weight),
          allocationDate: allocation.allocation_date
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération de la commande:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de la commande'
    });
  }
});

// @route   POST /api/orders
// @desc    Create new order
// @access  Private (Admin, Vendeur)
router.post('/', authenticate, authorize('admin', 'vendeur'), auditLog('CREATE_ORDER'), async (req, res) => {
  try {
    // Validate input
    const { error } = createOrderSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { clientId, orderDate, deliveryDate, items, notes } = req.body;

    // Verify client exists and is not blocked
    const client = await dbUtils.findById('clients', clientId);
    if (!client || !client.is_active) {
      return res.status(400).json({
        success: false,
        message: 'Client invalide ou inactif'
      });
    }

    if (client.is_blocked) {
      return res.status(400).json({
        success: false,
        message: `Client bloqué: ${client.block_reason || 'Raison non spécifiée'}`
      });
    }

    // Generate order number
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const orderCountQuery = `SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()`;
    const [orderCountResult] = await executeQuery(orderCountQuery);
    const dailyCount = orderCountResult.count + 1;
    const orderNumber = `CMD${today}${dailyCount.toString().padStart(4, '0')}`;

    // Calculate total amount
    const totalAmount = items.reduce((sum, item) => sum + (item.weight * item.unitPrice), 0);

    // Check credit limit
    const newBalance = client.current_balance + totalAmount;
    if (newBalance > client.credit_limit) {
      return res.status(400).json({
        success: false,
        message: `Limite de crédit dépassée. Limite: ${client.credit_limit}, Nouveau solde: ${newBalance}`
      });
    }

    // Verify stock availability and prepare allocations
    const stockAllocations = [];
    for (const item of items) {
      // Get available lots for this product (FIFO)
      const availableLotsQuery = `
        SELECT id, lot_number, current_quantity, current_weight, reception_date
        FROM reception_lots
        WHERE product_type_id = ? AND status = 'active' AND current_weight > 0
        ORDER BY reception_date ASC, created_at ASC
      `;
      
      const availableLots = await executeQuery(availableLotsQuery, [item.productTypeId]);
      
      let remainingWeight = item.weight;
      let remainingQuantity = item.quantity;
      
      for (const lot of availableLots) {
        if (remainingWeight <= 0) break;
        
        const allocatedWeight = Math.min(remainingWeight, lot.current_weight);
        const allocatedQuantity = Math.min(remainingQuantity, lot.current_quantity);
        
        stockAllocations.push({
          lotId: lot.id,
          productTypeId: item.productTypeId,
          allocatedQuantity,
          allocatedWeight
        });
        
        remainingWeight -= allocatedWeight;
        remainingQuantity -= allocatedQuantity;
      }
      
      if (remainingWeight > 0) {
        return res.status(400).json({
          success: false,
          message: `Stock insuffisant pour le produit ID ${item.productTypeId}. Manque: ${remainingWeight} kg`
        });
      }
    }

    // Create order and details in transaction
    const queries = [
      {
        query: `INSERT INTO orders 
          (order_number, client_id, order_date, delivery_date, status, total_amount, 
           paid_amount, remaining_amount, payment_status, notes, created_by, created_at, updated_at) 
          VALUES (?, ?, ?, ?, 'draft', ?, 0, ?, 'unpaid', ?, ?, NOW(), NOW())`,
        params: [orderNumber, clientId, orderDate, deliveryDate, totalAmount, totalAmount, notes, req.user.id]
      }
    ];

    const results = await executeTransaction(queries);
    const orderId = results[0].insertId;

    // Create order details
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const totalPrice = item.weight * item.unitPrice;
      
      const detailResult = await dbUtils.insert('order_details', {
        order_id: orderId,
        product_type_id: item.productTypeId,
        quantity: item.quantity,
        weight: item.weight,
        unit_price: item.unitPrice,
        total_price: totalPrice,
        notes: item.notes || null
      });

      // Create stock allocations for this detail
      const itemAllocations = stockAllocations.filter(a => a.productTypeId === item.productTypeId);
      for (const allocation of itemAllocations) {
        await dbUtils.insert('stock_allocations', {
          order_detail_id: detailResult.insertId,
          lot_id: allocation.lotId,
          allocated_quantity: allocation.allocatedQuantity,
          allocated_weight: allocation.allocatedWeight,
          allocation_date: new Date()
        });
      }
    }

    logger.info(`Nouvelle commande créée: ${orderNumber} (ID: ${orderId}) pour client ${client.name}`);

    res.status(201).json({
      success: true,
      message: 'Commande créée avec succès',
      data: {
        id: orderId,
        orderNumber,
        clientId,
        totalAmount,
        status: 'draft'
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création de la commande:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de la commande'
    });
  }
});

// @route   PUT /api/orders/:id/status
// @desc    Update order status
// @access  Private
router.put('/:id/status', authenticate, auditLog('UPDATE_ORDER_STATUS'), async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['draft', 'confirmed', 'prepared', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Statut invalide'
      });
    }

    // Get current order
    const order = await dbUtils.findById('orders', id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Commande non trouvée'
      });
    }

    const updateData = { status, updated_at: new Date() };

    // Set user fields based on status
    if (status === 'prepared' && !order.prepared_by) {
      updateData.prepared_by = req.user.id;
    } else if (status === 'delivered' && !order.delivered_by) {
      updateData.delivered_by = req.user.id;
    }

    // If confirming order, update stock and client balance
    if (status === 'confirmed' && order.status === 'draft') {
      // Update stock quantities
      const allocationsQuery = `
        SELECT sa.*, od.order_id
        FROM stock_allocations sa
        JOIN order_details od ON sa.order_detail_id = od.id
        WHERE od.order_id = ?
      `;
      
      const allocations = await executeQuery(allocationsQuery, [id]);
      
      for (const allocation of allocations) {
        // Update lot quantities
        await executeQuery(
          'UPDATE reception_lots SET current_quantity = current_quantity - ?, current_weight = current_weight - ?, updated_at = NOW() WHERE id = ?',
          [allocation.allocated_quantity, allocation.allocated_weight, allocation.lot_id]
        );

        // Create stock movement
        await dbUtils.insert('stock_movements', {
          lot_id: allocation.lot_id,
          movement_type: 'out',
          quantity_change: allocation.allocated_quantity,
          weight_change: allocation.allocated_weight,
          reference_type: 'order',
          reference_id: id,
          notes: `Commande ${order.order_number}`,
          created_by: req.user.id,
          created_at: new Date()
        });

        // Update lot status if depleted
        const [updatedLot] = await executeQuery('SELECT current_quantity, current_weight FROM reception_lots WHERE id = ?', [allocation.lot_id]);
        if (updatedLot.current_quantity <= 0 || updatedLot.current_weight <= 0) {
          await dbUtils.updateById('reception_lots', allocation.lot_id, { status: 'depleted' });
        }
      }

      // Update client balance
      await executeQuery(
        'UPDATE clients SET current_balance = current_balance + ?, unpaid_orders_count = unpaid_orders_count + 1, updated_at = NOW() WHERE id = ?',
        [order.total_amount, order.client_id]
      );
    }

    // Update order
    await dbUtils.updateById('orders', id, updateData);

    logger.info(`Statut de commande mis à jour: ${order.order_number} -> ${status}`);

    res.json({
      success: true,
      message: 'Statut de commande mis à jour avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du statut'
    });
  }
});

module.exports = router;
