import React from 'react';
import { Routes, Route, NavLink, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ClientsList from './ClientsList';
import ClientForm from './ClientForm';
import ClientDetails from './ClientDetails';

const ClientsPage = () => {
  const { t } = useTranslation();
  const location = useLocation();

  const tabs = [
    { name: t('clients.clientsList'), href: '/clients', icon: '👥' },
    { name: t('clients.newClient'), href: '/clients/new', icon: '➕' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('clients.title')}
        </h1>
        <p className="text-gray-600">
          Gestion des clients et de leurs informations
        </p>
      </div>

      {/* Navigation tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const isActive = location.pathname === tab.href ||
                (tab.href === '/clients' && location.pathname === '/clients');

              return (
                <NavLink
                  key={tab.name}
                  to={tab.href}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    isActive
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </NavLink>
              );
            })}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          <Routes>
            <Route index element={<ClientsList />} />
            <Route path="new" element={<ClientForm />} />
            <Route path="edit/:id" element={<ClientForm />} />
            <Route path=":id" element={<ClientDetails />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default ClientsPage;
