import React from 'react';
import { Routes, Route, NavLink, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import StockOverview from './StockOverview';
import StockLots from './StockLots';
import StockReception from './StockReception';
import StockMovements from './StockMovements';

const StockPage = () => {
  const { t } = useTranslation();
  const location = useLocation();

  const tabs = [
    { name: t('stock.overview'), href: '/stock', icon: '📊' },
    { name: t('stock.lots'), href: '/stock/lots', icon: '📦' },
    { name: t('stock.reception'), href: '/stock/reception', icon: '📥' },
    { name: t('stock.movements'), href: '/stock/movements', icon: '🔄' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('stock.title')}
        </h1>
        <p className="text-gray-600">
          Gestion du stock et des réceptions
        </p>
      </div>

      {/* Navigation tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const isActive = location.pathname === tab.href ||
                (tab.href === '/stock' && location.pathname === '/stock');

              return (
                <NavLink
                  key={tab.name}
                  to={tab.href}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    isActive
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </NavLink>
              );
            })}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          <Routes>
            <Route index element={<StockOverview />} />
            <Route path="lots" element={<StockLots />} />
            <Route path="reception" element={<StockReception />} />
            <Route path="movements" element={<StockMovements />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default StockPage;
