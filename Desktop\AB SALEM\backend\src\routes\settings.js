const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schema
const updateSettingSchema = Joi.object({
  settingValue: Joi.alternatives().try(
    Joi.string(),
    Joi.number(),
    Joi.boolean(),
    Joi.object()
  ).required()
});

// @route   GET /api/settings
// @desc    Get all system settings
// @access  Private (Admin only)
router.get('/', authenticate, authorize('admin'), async (req, res) => {
  try {
    const settings = await dbUtils.findAll('system_settings', {}, 
      'setting_key, setting_value, description_fr, description_ar, data_type, is_editable',
      'setting_key ASC');

    const formattedSettings = {};
    settings.forEach(setting => {
      let value = setting.setting_value;
      
      // Parse value based on data type
      switch (setting.data_type) {
        case 'number':
          value = parseFloat(value);
          break;
        case 'boolean':
          value = value === 'true';
          break;
        case 'json':
          try {
            value = JSON.parse(value);
          } catch (e) {
            value = setting.setting_value;
          }
          break;
        default:
          // Keep as string
          break;
      }

      formattedSettings[setting.setting_key] = {
        value: value,
        descriptionFr: setting.description_fr,
        descriptionAr: setting.description_ar,
        dataType: setting.data_type,
        isEditable: setting.is_editable
      };
    });

    res.json({
      success: true,
      data: formattedSettings
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des paramètres:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des paramètres'
    });
  }
});

// @route   GET /api/settings/:key
// @desc    Get specific setting by key
// @access  Private
router.get('/:key', authenticate, async (req, res) => {
  try {
    const { key } = req.params;

    const setting = await dbUtils.findAll('system_settings', { setting_key: key });

    if (setting.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Paramètre non trouvé'
      });
    }

    const settingData = setting[0];
    let value = settingData.setting_value;

    // Parse value based on data type
    switch (settingData.data_type) {
      case 'number':
        value = parseFloat(value);
        break;
      case 'boolean':
        value = value === 'true';
        break;
      case 'json':
        try {
          value = JSON.parse(value);
        } catch (e) {
          value = settingData.setting_value;
        }
        break;
    }

    res.json({
      success: true,
      data: {
        key: settingData.setting_key,
        value: value,
        descriptionFr: settingData.description_fr,
        descriptionAr: settingData.description_ar,
        dataType: settingData.data_type,
        isEditable: settingData.is_editable
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du paramètre:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du paramètre'
    });
  }
});

// @route   PUT /api/settings/:key
// @desc    Update system setting
// @access  Private (Admin only)
router.put('/:key', authenticate, authorize('admin'), auditLog('UPDATE_SETTING'), async (req, res) => {
  try {
    const { key } = req.params;

    // Validate input
    const { error } = updateSettingSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { settingValue } = req.body;

    // Check if setting exists
    const existingSetting = await dbUtils.findAll('system_settings', { setting_key: key });
    if (existingSetting.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Paramètre non trouvé'
      });
    }

    const setting = existingSetting[0];

    // Check if setting is editable
    if (!setting.is_editable) {
      return res.status(400).json({
        success: false,
        message: 'Ce paramètre ne peut pas être modifié'
      });
    }

    // Convert value to string based on data type
    let stringValue;
    switch (setting.data_type) {
      case 'number':
        if (typeof settingValue !== 'number') {
          return res.status(400).json({
            success: false,
            message: 'La valeur doit être un nombre'
          });
        }
        stringValue = settingValue.toString();
        break;
      case 'boolean':
        if (typeof settingValue !== 'boolean') {
          return res.status(400).json({
            success: false,
            message: 'La valeur doit être un booléen'
          });
        }
        stringValue = settingValue.toString();
        break;
      case 'json':
        if (typeof settingValue !== 'object') {
          return res.status(400).json({
            success: false,
            message: 'La valeur doit être un objet JSON'
          });
        }
        stringValue = JSON.stringify(settingValue);
        break;
      default:
        stringValue = settingValue.toString();
        break;
    }

    // Update setting
    await dbUtils.updateById('system_settings', setting.id, {
      setting_value: stringValue,
      updated_at: new Date()
    });

    logger.info(`Paramètre système mis à jour: ${key} = ${stringValue}`);

    res.json({
      success: true,
      message: 'Paramètre mis à jour avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour du paramètre:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du paramètre'
    });
  }
});

// @route   GET /api/settings/backup/create
// @desc    Create manual backup
// @access  Private (Admin only)
router.post('/backup/create', authenticate, authorize('admin'), auditLog('CREATE_BACKUP'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const { spawn } = require('child_process');

    // Create backup directory if it doesn't exist
    const backupDir = path.join(__dirname, '../../../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Generate backup filename
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const backupFilename = `backup-${timestamp}.sql`;
    const backupPath = path.join(backupDir, backupFilename);

    // MySQL dump command
    const mysqldump = spawn('mysqldump', [
      '-h', process.env.DB_HOST || 'localhost',
      '-P', process.env.DB_PORT || '3306',
      '-u', process.env.DB_USER || 'root',
      ...(process.env.DB_PASSWORD ? ['-p' + process.env.DB_PASSWORD] : []),
      '--single-transaction',
      '--routines',
      '--triggers',
      process.env.DB_NAME || 'abattoir_salem'
    ]);

    const writeStream = fs.createWriteStream(backupPath);
    mysqldump.stdout.pipe(writeStream);

    let errorOutput = '';
    mysqldump.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    mysqldump.on('close', (code) => {
      if (code === 0) {
        logger.info(`Sauvegarde créée avec succès: ${backupFilename}`);
        res.json({
          success: true,
          message: 'Sauvegarde créée avec succès',
          data: {
            filename: backupFilename,
            path: backupPath,
            size: fs.statSync(backupPath).size
          }
        });
      } else {
        logger.error(`Erreur lors de la création de la sauvegarde: ${errorOutput}`);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la création de la sauvegarde'
        });
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création de la sauvegarde:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de la sauvegarde'
    });
  }
});

// @route   GET /api/settings/backup/list
// @desc    List available backups
// @access  Private (Admin only)
router.get('/backup/list', authenticate, authorize('admin'), async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');

    const backupDir = path.join(__dirname, '../../../backups');
    
    if (!fs.existsSync(backupDir)) {
      return res.json({
        success: true,
        data: []
      });
    }

    const files = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          size: stats.size,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime
        };
      })
      .sort((a, b) => b.createdAt - a.createdAt);

    res.json({
      success: true,
      data: files
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération de la liste des sauvegardes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de la liste des sauvegardes'
    });
  }
});

// @route   GET /api/settings/audit-logs
// @desc    Get audit logs
// @access  Private (Admin only)
router.get('/audit-logs', authenticate, authorize('admin'), async (req, res) => {
  try {
    const { page = 1, limit = 50, userId = '', action = '', dateFrom = '', dateTo = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (userId) {
      whereConditions.push('al.user_id = ?');
      params.push(userId);
    }

    if (action) {
      whereConditions.push('al.action LIKE ?');
      params.push(`%${action}%`);
    }

    if (dateFrom) {
      whereConditions.push('DATE(al.created_at) >= ?');
      params.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('DATE(al.created_at) <= ?');
      params.push(dateTo);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM audit_logs al ${whereClause}`;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get audit logs
    const query = `
      SELECT 
        al.*,
        u.full_name as user_name,
        u.username
      FROM audit_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ${whereClause}
      ORDER BY al.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const logs = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        logs: logs.map(log => ({
          id: log.id,
          userId: log.user_id,
          userName: log.user_name,
          username: log.username,
          action: log.action,
          tableName: log.table_name,
          recordId: log.record_id,
          oldValues: log.old_values ? JSON.parse(log.old_values) : null,
          newValues: log.new_values ? JSON.parse(log.new_values) : null,
          ipAddress: log.ip_address,
          userAgent: log.user_agent,
          createdAt: log.created_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des logs d\'audit:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des logs d\'audit'
    });
  }
});

module.exports = router;
