const express = require('express');
const Joi = require('joi');
const { dbUtils, executeQuery } = require('../config/database');
const { authenticate, authorize, auditLog } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const createClientSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  phone: Joi.string().max(20).optional(),
  email: Joi.string().email().optional(),
  address: Joi.string().optional(),
  creditLimit: Joi.number().min(0).default(0),
  notes: Joi.string().optional()
});

const updateClientSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  phone: Joi.string().max(20).optional(),
  email: Joi.string().email().optional(),
  address: Joi.string().optional(),
  creditLimit: Joi.number().min(0).optional(),
  isBlocked: Joi.boolean().optional(),
  blockReason: Joi.string().optional(),
  notes: Joi.string().optional(),
  isActive: Joi.boolean().optional()
});

// @route   GET /api/clients
// @desc    Get all clients
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', blocked = '', active = 'true' } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let params = [];

    if (search) {
      whereConditions.push('(name LIKE ? OR phone LIKE ? OR email LIKE ? OR client_code LIKE ?)');
      params.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (blocked === 'true') {
      whereConditions.push('is_blocked = true');
    } else if (blocked === 'false') {
      whereConditions.push('is_blocked = false');
    }

    if (active === 'true') {
      whereConditions.push('is_active = true');
    } else if (active === 'false') {
      whereConditions.push('is_active = false');
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM clients ${whereClause}`;
    const [countResult] = await executeQuery(countQuery, params);
    const total = countResult.total;

    // Get clients
    const query = `
      SELECT *
      FROM clients 
      ${whereClause}
      ORDER BY name ASC 
      LIMIT ? OFFSET ?
    `;
    
    const clients = await executeQuery(query, [...params, parseInt(limit), parseInt(offset)]);

    res.json({
      success: true,
      data: {
        clients: clients.map(client => ({
          id: client.id,
          clientCode: client.client_code,
          name: client.name,
          phone: client.phone,
          email: client.email,
          address: client.address,
          creditLimit: parseFloat(client.credit_limit),
          currentBalance: parseFloat(client.current_balance),
          unpaidOrdersCount: client.unpaid_orders_count,
          isBlocked: client.is_blocked,
          blockReason: client.block_reason,
          notes: client.notes,
          isActive: client.is_active,
          createdAt: client.created_at,
          updatedAt: client.updated_at
        })),
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total: total
        }
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des clients'
    });
  }
});

// @route   GET /api/clients/:id
// @desc    Get client by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    const client = await dbUtils.findById('clients', id);

    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    // Get client's recent orders
    const recentOrdersQuery = `
      SELECT id, order_number, order_date, total_amount, paid_amount, payment_status, status
      FROM orders 
      WHERE client_id = ? 
      ORDER BY order_date DESC 
      LIMIT 5
    `;
    const recentOrders = await executeQuery(recentOrdersQuery, [id]);

    res.json({
      success: true,
      data: {
        id: client.id,
        clientCode: client.client_code,
        name: client.name,
        phone: client.phone,
        email: client.email,
        address: client.address,
        creditLimit: parseFloat(client.credit_limit),
        currentBalance: parseFloat(client.current_balance),
        unpaidOrdersCount: client.unpaid_orders_count,
        isBlocked: client.is_blocked,
        blockReason: client.block_reason,
        notes: client.notes,
        isActive: client.is_active,
        createdAt: client.created_at,
        updatedAt: client.updated_at,
        recentOrders: recentOrders.map(order => ({
          id: order.id,
          orderNumber: order.order_number,
          orderDate: order.order_date,
          totalAmount: parseFloat(order.total_amount),
          paidAmount: parseFloat(order.paid_amount),
          paymentStatus: order.payment_status,
          status: order.status
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du client'
    });
  }
});

// @route   POST /api/clients
// @desc    Create new client
// @access  Private (Admin, Vendeur)
router.post('/', authenticate, authorize('admin', 'vendeur'), auditLog('CREATE_CLIENT'), async (req, res) => {
  try {
    // Validate input
    const { error } = createClientSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { name, phone, email, address, creditLimit, notes } = req.body;

    // Generate client code
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const clientCountQuery = `SELECT COUNT(*) as count FROM clients WHERE DATE(created_at) = CURDATE()`;
    const [clientCountResult] = await executeQuery(clientCountQuery);
    const dailyCount = clientCountResult.count + 1;
    const clientCode = `CLI${today}${dailyCount.toString().padStart(3, '0')}`;

    // Check if phone already exists (if provided)
    if (phone) {
      const existingPhone = await dbUtils.findAll('clients', { phone });
      if (existingPhone.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Un client avec ce numéro de téléphone existe déjà'
        });
      }
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await dbUtils.findAll('clients', { email });
      if (existingEmail.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Un client avec cette adresse email existe déjà'
        });
      }
    }

    // Create client
    const clientData = {
      client_code: clientCode,
      name,
      phone: phone || null,
      email: email || null,
      address: address || null,
      credit_limit: creditLimit || 0,
      current_balance: 0,
      unpaid_orders_count: 0,
      is_blocked: false,
      notes: notes || null,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await dbUtils.insert('clients', clientData);

    logger.info(`Nouveau client créé: ${name} (${clientCode}) - ID: ${result.insertId}`);

    res.status(201).json({
      success: true,
      message: 'Client créé avec succès',
      data: {
        id: result.insertId,
        clientCode,
        name,
        phone,
        email,
        creditLimit
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la création du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du client'
    });
  }
});

// @route   PUT /api/clients/:id
// @desc    Update client
// @access  Private (Admin, Vendeur)
router.put('/:id', authenticate, authorize('admin', 'vendeur'), auditLog('UPDATE_CLIENT'), async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateClientSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // Check if client exists
    const existingClient = await dbUtils.findById('clients', id);
    if (!existingClient) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const { name, phone, email, address, creditLimit, isBlocked, blockReason, notes, isActive } = req.body;
    const updateData = {};

    // Check phone uniqueness if changed
    if (phone && phone !== existingClient.phone) {
      const phoneExists = await dbUtils.findAll('clients', { phone });
      if (phoneExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Un client avec ce numéro de téléphone existe déjà'
        });
      }
      updateData.phone = phone;
    }

    // Check email uniqueness if changed
    if (email && email !== existingClient.email) {
      const emailExists = await dbUtils.findAll('clients', { email });
      if (emailExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Un client avec cette adresse email existe déjà'
        });
      }
      updateData.email = email;
    }

    if (name) updateData.name = name;
    if (address !== undefined) updateData.address = address;
    if (typeof creditLimit === 'number') updateData.credit_limit = creditLimit;
    if (typeof isBlocked === 'boolean') {
      updateData.is_blocked = isBlocked;
      if (!isBlocked) {
        updateData.block_reason = null;
      }
    }
    if (blockReason !== undefined) updateData.block_reason = blockReason;
    if (notes !== undefined) updateData.notes = notes;
    if (typeof isActive === 'boolean') updateData.is_active = isActive;
    
    updateData.updated_at = new Date();

    // Update client
    await dbUtils.updateById('clients', id, updateData);

    logger.info(`Client mis à jour: ${existingClient.name} (${existingClient.client_code}) - ID: ${id}`);

    res.json({
      success: true,
      message: 'Client mis à jour avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du client'
    });
  }
});

// @route   GET /api/clients/:id/balance
// @desc    Get client balance and payment history
// @access  Private
router.get('/:id/balance', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if client exists
    const client = await dbUtils.findById('clients', id, 'id, name, client_code, current_balance, credit_limit');
    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    // Get payment history
    const paymentsQuery = `
      SELECT p.*, o.order_number
      FROM payments p
      LEFT JOIN orders o ON p.order_id = o.id
      WHERE p.client_id = ?
      ORDER BY p.payment_date DESC
      LIMIT 10
    `;
    const payments = await executeQuery(paymentsQuery, [id]);

    // Get unpaid orders
    const unpaidOrdersQuery = `
      SELECT id, order_number, order_date, total_amount, paid_amount, remaining_amount
      FROM orders
      WHERE client_id = ? AND payment_status IN ('unpaid', 'partial')
      ORDER BY order_date ASC
    `;
    const unpaidOrders = await executeQuery(unpaidOrdersQuery, [id]);

    res.json({
      success: true,
      data: {
        client: {
          id: client.id,
          name: client.name,
          clientCode: client.client_code,
          currentBalance: parseFloat(client.current_balance),
          creditLimit: parseFloat(client.credit_limit)
        },
        payments: payments.map(payment => ({
          id: payment.id,
          paymentNumber: payment.payment_number,
          orderNumber: payment.order_number,
          paymentDate: payment.payment_date,
          amount: parseFloat(payment.amount),
          paymentMethod: payment.payment_method,
          reference: payment.reference,
          notes: payment.notes
        })),
        unpaidOrders: unpaidOrders.map(order => ({
          id: order.id,
          orderNumber: order.order_number,
          orderDate: order.order_date,
          totalAmount: parseFloat(order.total_amount),
          paidAmount: parseFloat(order.paid_amount),
          remainingAmount: parseFloat(order.remaining_amount)
        }))
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du solde client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du solde client'
    });
  }
});

module.exports = router;
