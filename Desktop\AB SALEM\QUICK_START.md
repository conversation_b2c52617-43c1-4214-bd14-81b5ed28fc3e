# 🚀 Guide de démarrage rapide - AbattoirSalem

## ⚡ Installation en 5 minutes

### 1. Prérequis
- **Node.js** v16+ ([Télécharger](https://nodejs.org/))
- **MySQL/MariaDB** ([Télécharger MySQL](https://dev.mysql.com/downloads/installer/) ou [MariaDB](https://mariadb.org/download/))
- **Git** (optionnel)

### 2. Installation automatique

```bash
# Exécuter le script d'installation
./install.bat
```

### 3. Configuration de la base de données

1. **Démarrer MySQL/MariaDB**
2. **Créer la base de données** :
```sql
mysql -u root -p < db/init.sql
```

3. **Configurer la connexion** dans `.env` :
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=abattoir_salem
DB_USER=root
DB_PASSWORD=votre_mot_de_passe
```

### 4. Démarrage

```bash
# Démarrage automatique (recommandé)
./start.bat

# OU démarrage manuel
npm run dev
```

### 5. Première connexion

- **URL** : L'application s'ouvre automatiquement
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`

## 🎯 Fonctionnalités principales

### Tableau de bord
- Vue d'ensemble des activités
- Statistiques en temps réel
- Alertes importantes

### Gestion du stock
- Réception de marchandises
- Suivi des lots (FIFO)
- Alertes de stock bas

### Clients et commandes
- Gestion des clients
- Création de commandes
- Suivi des livraisons

### Paiements
- Enregistrement des paiements
- Gestion du crédit
- Journal de caisse

## 🔧 Configuration rapide

### Paramètres essentiels

1. **Aller dans Paramètres** (menu de gauche)
2. **Configurer** :
   - Nom de l'entreprise
   - Devise (DZD par défaut)
   - Seuil de stock bas
   - Limite de crédit par défaut

### Ajouter des utilisateurs

1. **Aller dans Utilisateurs** (admin uniquement)
2. **Créer des comptes** pour :
   - Vendeurs
   - Préparateurs
   - Magasiniers

### Ajouter des produits

1. **Aller dans Produits**
2. **Ajouter les types** de viande :
   - Bœuf, Mouton, Chèvre, Veau
   - Unités : kg, pièce, ou les deux

## 📱 Interface

### Navigation
- **Sidebar gauche** : Menu principal
- **Header** : Changement de langue, notifications, profil
- **Contenu principal** : Pages de travail

### Langues
- **Français** : Interface par défaut
- **Arabe** : Support RTL complet
- **Basculer** : Bouton en haut à droite

### Raccourcis clavier
- `Ctrl+N` : Nouvelle commande
- `Ctrl+S` : Sauvegarde
- `F5` : Actualiser

## 🔒 Sécurité

### Rôles utilisateurs
- **Admin** : Accès complet
- **Vendeur** : Commandes, clients, paiements
- **Préparateur** : Préparation des commandes
- **Magasinier** : Stock, réceptions

### Bonnes pratiques
- Changer le mot de passe admin par défaut
- Créer des comptes séparés pour chaque utilisateur
- Faire des sauvegardes régulières

## 💾 Sauvegarde

### Automatique
- Sauvegarde quotidienne à 2h du matin
- Fichiers dans `/backups/`
- Rétention : 30 jours par défaut

### Manuelle
1. **Aller dans Paramètres > Sauvegarde**
2. **Cliquer sur "Créer une sauvegarde"**
3. **Télécharger le fichier** généré

## 🆘 Dépannage

### L'application ne démarre pas
1. Vérifier que MySQL est démarré
2. Vérifier la configuration `.env`
3. Consulter les logs dans `/logs/`

### Erreur de connexion à la base
1. Tester la connexion MySQL
2. Vérifier les identifiants dans `.env`
3. S'assurer que la base `abattoir_salem` existe

### Interface en anglais
1. Cliquer sur le bouton langue (en haut à droite)
2. Ou aller dans Paramètres > Langue

### Problème de performance
1. Redémarrer l'application
2. Vérifier l'espace disque
3. Nettoyer les anciens logs

## 📞 Support

### Logs système
- **Emplacement** : `/logs/`
- **Types** : `error.log`, `combined.log`
- **Niveau** : Configurable dans `.env`

### Base de données
- **Sauvegarde** : Automatique quotidienne
- **Restauration** : Via interface admin
- **Maintenance** : Scripts SQL fournis

### Mise à jour
- **Méthode** : Manuelle via clé USB
- **Sauvegarde** : Automatique avant mise à jour
- **Rollback** : Possible via sauvegarde

---

**🎉 Félicitations !** Votre système AbattoirSalem est prêt à l'emploi.

Pour une utilisation avancée, consultez le `README.md` complet.
