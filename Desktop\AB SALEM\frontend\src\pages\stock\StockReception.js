import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import toast from 'react-hot-toast';

const StockReception = () => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    productTypeId: '',
    receptionDate: new Date().toISOString().slice(0, 10),
    initialQuantity: '',
    initialWeight: '',
    unitPrice: '',
    supplierName: '',
    notes: '',
    expiryDate: ''
  });

  const [errors, setErrors] = useState({});

  // Fetch products
  const { data: productsData, isLoading: productsLoading } = useQuery(
    'products',
    () => apiService.products.getAll({ active: 'true' }, token),
    {
      enabled: !!token,
    }
  );

  // Create reception mutation
  const createReceptionMutation = useMutation(
    (data) => apiService.stock.createReception(data, token),
    {
      onSuccess: (response) => {
        if (response.success) {
          toast.success('Réception enregistrée avec succès');
          // Reset form
          setFormData({
            productTypeId: '',
            receptionDate: new Date().toISOString().slice(0, 10),
            initialQuantity: '',
            initialWeight: '',
            unitPrice: '',
            supplierName: '',
            notes: '',
            expiryDate: ''
          });
          setErrors({});
          // Invalidate related queries
          queryClient.invalidateQueries('stock-summary');
          queryClient.invalidateQueries('stock-lots');
        } else {
          toast.error(response.message || 'Erreur lors de l\'enregistrement');
        }
      },
      onError: (error) => {
        toast.error(error.message || 'Erreur lors de l\'enregistrement');
      }
    }
  );

  const products = productsData?.data || [];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productTypeId) {
      newErrors.productTypeId = 'Le type de produit est requis';
    }
    if (!formData.receptionDate) {
      newErrors.receptionDate = 'La date de réception est requise';
    }
    if (!formData.initialQuantity || formData.initialQuantity <= 0) {
      newErrors.initialQuantity = 'La quantité doit être supérieure à 0';
    }
    if (!formData.initialWeight || formData.initialWeight <= 0) {
      newErrors.initialWeight = 'Le poids doit être supérieur à 0';
    }
    if (formData.unitPrice && formData.unitPrice < 0) {
      newErrors.unitPrice = 'Le prix unitaire ne peut pas être négatif';
    }
    if (formData.expiryDate && new Date(formData.expiryDate) <= new Date(formData.receptionDate)) {
      newErrors.expiryDate = 'La date d\'expiration doit être postérieure à la date de réception';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submissionData = {
      productTypeId: parseInt(formData.productTypeId),
      receptionDate: formData.receptionDate,
      initialQuantity: parseInt(formData.initialQuantity),
      initialWeight: parseFloat(formData.initialWeight),
      unitPrice: formData.unitPrice ? parseFloat(formData.unitPrice) : null,
      supplierName: formData.supplierName || null,
      notes: formData.notes || null,
      expiryDate: formData.expiryDate || null
    };

    createReceptionMutation.mutate(submissionData);
  };

  if (productsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {t('stock.newReception')}
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Enregistrer une nouvelle réception de marchandises
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Product Type */}
          <div>
            <label htmlFor="productTypeId" className="form-label">
              {t('stock.productType')} *
            </label>
            <select
              id="productTypeId"
              name="productTypeId"
              value={formData.productTypeId}
              onChange={handleInputChange}
              className={`form-select ${errors.productTypeId ? 'border-red-300' : ''}`}
              disabled={createReceptionMutation.isLoading}
            >
              <option value="">Sélectionner un produit</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.nameFr} {product.nameAr && `(${product.nameAr})`}
                </option>
              ))}
            </select>
            {errors.productTypeId && (
              <p className="form-error">{errors.productTypeId}</p>
            )}
          </div>

          {/* Reception Date */}
          <div>
            <label htmlFor="receptionDate" className="form-label">
              {t('stock.receptionDate')} *
            </label>
            <input
              type="date"
              id="receptionDate"
              name="receptionDate"
              value={formData.receptionDate}
              onChange={handleInputChange}
              className={`form-input ${errors.receptionDate ? 'border-red-300' : ''}`}
              disabled={createReceptionMutation.isLoading}
            />
            {errors.receptionDate && (
              <p className="form-error">{errors.receptionDate}</p>
            )}
          </div>

          {/* Quantity and Weight */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="initialQuantity" className="form-label">
                {t('stock.initialQuantity')} (pièces) *
              </label>
              <input
                type="number"
                id="initialQuantity"
                name="initialQuantity"
                value={formData.initialQuantity}
                onChange={handleInputChange}
                min="1"
                step="1"
                className={`form-input ${errors.initialQuantity ? 'border-red-300' : ''}`}
                disabled={createReceptionMutation.isLoading}
              />
              {errors.initialQuantity && (
                <p className="form-error">{errors.initialQuantity}</p>
              )}
            </div>

            <div>
              <label htmlFor="initialWeight" className="form-label">
                {t('stock.initialWeight')} (kg) *
              </label>
              <input
                type="number"
                id="initialWeight"
                name="initialWeight"
                value={formData.initialWeight}
                onChange={handleInputChange}
                min="0.1"
                step="0.1"
                className={`form-input ${errors.initialWeight ? 'border-red-300' : ''}`}
                disabled={createReceptionMutation.isLoading}
              />
              {errors.initialWeight && (
                <p className="form-error">{errors.initialWeight}</p>
              )}
            </div>
          </div>

          {/* Unit Price */}
          <div>
            <label htmlFor="unitPrice" className="form-label">
              {t('stock.unitPrice')} (DZD/kg)
            </label>
            <input
              type="number"
              id="unitPrice"
              name="unitPrice"
              value={formData.unitPrice}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className={`form-input ${errors.unitPrice ? 'border-red-300' : ''}`}
              disabled={createReceptionMutation.isLoading}
            />
            {errors.unitPrice && (
              <p className="form-error">{errors.unitPrice}</p>
            )}
          </div>

          {/* Supplier */}
          <div>
            <label htmlFor="supplierName" className="form-label">
              {t('stock.supplier')}
            </label>
            <input
              type="text"
              id="supplierName"
              name="supplierName"
              value={formData.supplierName}
              onChange={handleInputChange}
              className="form-input"
              disabled={createReceptionMutation.isLoading}
            />
          </div>

          {/* Expiry Date */}
          <div>
            <label htmlFor="expiryDate" className="form-label">
              {t('stock.expiryDate')}
            </label>
            <input
              type="date"
              id="expiryDate"
              name="expiryDate"
              value={formData.expiryDate}
              onChange={handleInputChange}
              className={`form-input ${errors.expiryDate ? 'border-red-300' : ''}`}
              disabled={createReceptionMutation.isLoading}
            />
            {errors.expiryDate && (
              <p className="form-error">{errors.expiryDate}</p>
            )}
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="form-label">
              {t('common.notes')}
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="form-textarea"
              disabled={createReceptionMutation.isLoading}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => {
                setFormData({
                  productTypeId: '',
                  receptionDate: new Date().toISOString().slice(0, 10),
                  initialQuantity: '',
                  initialWeight: '',
                  unitPrice: '',
                  supplierName: '',
                  notes: '',
                  expiryDate: ''
                });
                setErrors({});
              }}
              className="btn-outline"
              disabled={createReceptionMutation.isLoading}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="btn-primary flex items-center space-x-2"
              disabled={createReceptionMutation.isLoading}
            >
              {createReceptionMutation.isLoading && <LoadingSpinner size="sm" />}
              <span>{t('common.save')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockReception;
