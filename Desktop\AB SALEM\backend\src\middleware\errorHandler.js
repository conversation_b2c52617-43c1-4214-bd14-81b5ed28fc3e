const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error(err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Ressource non trouvée';
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Ressource déjà existante';
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = { message, statusCode: 400 };
  }

  // MySQL errors
  if (err.code) {
    switch (err.code) {
      case 'ER_DUP_ENTRY':
        error = { 
          message: 'Cette entrée existe déjà', 
          statusCode: 400 
        };
        break;
      case 'ER_NO_REFERENCED_ROW_2':
        error = { 
          message: 'Référence invalide', 
          statusCode: 400 
        };
        break;
      case 'ER_ROW_IS_REFERENCED_2':
        error = { 
          message: 'Impossible de supprimer, des références existent', 
          statusCode: 400 
        };
        break;
      case 'ER_DATA_TOO_LONG':
        error = { 
          message: 'Données trop longues pour le champ', 
          statusCode: 400 
        };
        break;
      case 'ER_BAD_NULL_ERROR':
        error = { 
          message: 'Champ requis manquant', 
          statusCode: 400 
        };
        break;
      case 'ECONNREFUSED':
        error = { 
          message: 'Erreur de connexion à la base de données', 
          statusCode: 500 
        };
        break;
      default:
        error = { 
          message: 'Erreur de base de données', 
          statusCode: 500 
        };
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = { 
      message: 'Token invalide', 
      statusCode: 401 
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = { 
      message: 'Token expiré', 
      statusCode: 401 
    };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Erreur serveur',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = errorHandler;
