import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import frTranslations from './locales/fr.json';
import arTranslations from './locales/ar.json';

const resources = {
  fr: {
    translation: frTranslations
  },
  ar: {
    translation: arTranslations
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'fr',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false, // React already does escaping
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    
    react: {
      useSuspense: false,
    },
  });

// Update document direction and language when language changes
i18n.on('languageChanged', (lng) => {
  const isRTL = lng === 'ar';
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  document.documentElement.lang = lng;
  
  // Update font family for Arabic
  if (isRTL) {
    document.documentElement.style.fontFamily = "'Noto Sans Arabic', system-ui, sans-serif";
  } else {
    document.documentElement.style.fontFamily = "'Inter', system-ui, sans-serif";
  }
});

// Set initial direction and language
const currentLang = i18n.language || 'fr';
const isRTL = currentLang === 'ar';
document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
document.documentElement.lang = currentLang;

export default i18n;
