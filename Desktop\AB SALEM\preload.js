const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),

  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Menu events
  onMenuNewOrder: (callback) => ipcRenderer.on('menu-new-order', callback),
  onMenuBackup: (callback) => ipcRenderer.on('menu-backup', callback),
  onMenuStock: (callback) => ipcRenderer.on('menu-stock', callback),
  onMenuClients: (callback) => ipcRenderer.on('menu-clients', callback),
  onMenuOrders: (callback) => ipcRenderer.on('menu-orders', callback),
  onMenuSettings: (callback) => ipcRenderer.on('menu-settings', callback),
  onMenuLogs: (callback) => ipcRenderer.on('menu-logs', callback),

  // Window events
  onWindowMaximized: (callback) => ipcRenderer.on('window-maximized', callback),
  onWindowUnmaximized: (callback) => ipcRenderer.on('window-unmaximized', callback),

  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Expose a limited API for backend communication
contextBridge.exposeInMainWorld('backendAPI', {
  // Base URL for API calls
  baseURL: 'http://localhost:3001/api',
  
  // HTTP methods wrapper (will be used by frontend)
  request: async (method, endpoint, data = null, token = null) => {
    const url = `http://localhost:3001/api${endpoint}`;
    const options = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    if (data && (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Request failed');
      }
      
      return result;
    } catch (error) {
      throw new Error(error.message || 'Network error');
    }
  }
});
