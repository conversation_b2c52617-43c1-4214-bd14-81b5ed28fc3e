{"name": "abattoir-salem-frontend", "version": "1.0.0", "description": "Interface utilisateur pour AbattoirSalem", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.17.0", "react-i18next": "^13.5.0", "i18next": "^23.6.0", "i18next-browser-languagedetector": "^7.2.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-to-print": "^2.14.15", "jspdf": "^2.5.1", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "devDependencies": {"react-scripts": "5.0.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "./"}